import { useState, useEffect, useCallback } from 'react';
import { generateCSRFToken, validateCSRFToken } from '@/utils/securityUtils';

interface CSRFProtectionOptions {
  autoGenerate?: boolean;
  storageKey?: string;
  expirationTime?: number; // in milliseconds
}

interface CSRFProtectionReturn {
  token: string | null;
  isValid: boolean;
  generateToken: () => string;
  validateToken: (tokenToValidate: string) => boolean;
  clearToken: () => void;
  isExpired: boolean;
}

/**
 * Hook for CSRF protection in forms
 * Generates and validates CSRF tokens to prevent cross-site request forgery attacks
 */
export const useCSRFProtection = (options: CSRFProtectionOptions = {}): CSRFProtectionReturn => {
  const {
    autoGenerate = true,
    storageKey = 'csrf_token',
    expirationTime = 30 * 60 * 1000, // 30 minutes default
  } = options;

  const [token, setToken] = useState<string | null>(null);
  const [tokenTimestamp, setTokenTimestamp] = useState<number | null>(null);
  const [isValid, setIsValid] = useState<boolean>(false);

  // Check if token is expired
  const isExpired = tokenTimestamp ? Date.now() - tokenTimestamp > expirationTime : true;

  // Generate a new CSRF token
  const generateToken = useCallback((): string => {
    const newToken = generateCSRFToken();
    const timestamp = Date.now();
    
    setToken(newToken);
    setTokenTimestamp(timestamp);
    setIsValid(true);
    
    // Store in sessionStorage for form persistence
    try {
      sessionStorage.setItem(storageKey, newToken);
      sessionStorage.setItem(`${storageKey}_timestamp`, timestamp.toString());
    } catch (error) {
      console.warn('Failed to store CSRF token in sessionStorage:', error);
    }
    
    return newToken;
  }, [storageKey]);

  // Validate a CSRF token
  const validateToken = useCallback((tokenToValidate: string): boolean => {
    if (!token || !tokenToValidate) {
      setIsValid(false);
      return false;
    }

    if (isExpired) {
      setIsValid(false);
      console.warn('CSRF token has expired');
      return false;
    }

    const isValidToken = validateCSRFToken(tokenToValidate, token);
    setIsValid(isValidToken);
    
    if (!isValidToken) {
      console.warn('CSRF token validation failed');
    }
    
    return isValidToken;
  }, [token, isExpired]);

  // Clear the CSRF token
  const clearToken = useCallback(() => {
    setToken(null);
    setTokenTimestamp(null);
    setIsValid(false);
    
    try {
      sessionStorage.removeItem(storageKey);
      sessionStorage.removeItem(`${storageKey}_timestamp`);
    } catch (error) {
      console.warn('Failed to clear CSRF token from sessionStorage:', error);
    }
  }, [storageKey]);

  // Initialize token on mount
  useEffect(() => {
    // Try to restore token from sessionStorage
    try {
      const storedToken = sessionStorage.getItem(storageKey);
      const storedTimestamp = sessionStorage.getItem(`${storageKey}_timestamp`);
      
      if (storedToken && storedTimestamp) {
        const timestamp = parseInt(storedTimestamp, 10);
        const tokenAge = Date.now() - timestamp;
        
        if (tokenAge < expirationTime) {
          setToken(storedToken);
          setTokenTimestamp(timestamp);
          setIsValid(true);
          return;
        } else {
          // Token expired, clear it
          sessionStorage.removeItem(storageKey);
          sessionStorage.removeItem(`${storageKey}_timestamp`);
        }
      }
    } catch (error) {
      console.warn('Failed to restore CSRF token from sessionStorage:', error);
    }
    
    // Generate new token if auto-generate is enabled
    if (autoGenerate) {
      generateToken();
    }
  }, [autoGenerate, generateToken, storageKey, expirationTime]);

  // Auto-refresh token when it expires
  useEffect(() => {
    if (isExpired && autoGenerate && token) {
      console.log('CSRF token expired, generating new token');
      generateToken();
    }
  }, [isExpired, autoGenerate, token, generateToken]);

  return {
    token,
    isValid,
    generateToken,
    validateToken,
    clearToken,
    isExpired,
  };
};

/**
 * Higher-order component for CSRF protection
 * Wraps form components with automatic CSRF token handling
 */
export const withCSRFProtection = <T extends object>(
  WrappedComponent: React.ComponentType<T>,
  options?: CSRFProtectionOptions
) => {
  return (props: T) => {
    const csrfProtection = useCSRFProtection(options);
    
    return (
      <WrappedComponent
        {...props}
        csrfProtection={csrfProtection}
      />
    );
  };
};

export default useCSRFProtection;
