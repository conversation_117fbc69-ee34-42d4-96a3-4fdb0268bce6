import { useEffect, useCallback } from 'react';
import { useRecommendations } from '@/contexts/RecommendationContext';
import { useCart } from '@/contexts/CartContext';
import { useAuth } from '@/contexts/AuthContext';
import { logger } from '@/utils/logger';
import { getSessionId } from '@/utils/recommendationUtils';

/**
 * Hook to automatically track user behavior for recommendations
 */
export const useRecommendationTracking = () => {
  const { trackCartAddition } = useRecommendations();
  const { items: cartItems } = useCart();
  const { user } = useAuth();

  // Track cart changes for recommendations
  useEffect(() => {
    // This effect will run whenever cart items change
    // We can use this to track cart additions, but we need to be careful
    // not to track on initial load or when items are removed
    
    // For now, we'll rely on manual tracking in the addItem function
    // This could be enhanced to detect new items vs. quantity changes
  }, [cartItems]);

  /**
   * Track product view with enhanced analytics
   */
  const trackProductViewEnhanced = useCallback((
    productId: string, 
    duration?: number,
    source?: string
  ) => {
    try {
      // Use the recommendation context tracking
      const { trackProductView } = useRecommendations();
      trackProductView(productId, duration);
      
      // Additional analytics logging
      logger.userAction('product_view_enhanced', {
        productId,
        duration,
        source,
        sessionId: getSessionId(),
        userId: user?.sub,
        timestamp: Date.now()
      });
    } catch (error) {
      logger.error('Failed to track enhanced product view', error);
    }
  }, [user]);

  /**
   * Track cart addition with enhanced analytics
   */
  const trackCartAdditionEnhanced = useCallback((
    productId: string, 
    quantity: number,
    source?: string
  ) => {
    try {
      // Use the recommendation context tracking
      trackCartAddition(productId, quantity);
      
      // Additional analytics logging
      logger.userAction('cart_addition_enhanced', {
        productId,
        quantity,
        source,
        sessionId: getSessionId(),
        userId: user?.sub,
        timestamp: Date.now()
      });
    } catch (error) {
      logger.error('Failed to track enhanced cart addition', error);
    }
  }, [trackCartAddition, user]);

  /**
   * Track recommendation click
   */
  const trackRecommendationClick = useCallback((
    productId: string,
    recommendationType: string,
    position: number,
    sourceProductId?: string
  ) => {
    try {
      logger.userAction('recommendation_click', {
        productId,
        recommendationType,
        position,
        sourceProductId,
        sessionId: getSessionId(),
        userId: user?.sub,
        timestamp: Date.now()
      });
    } catch (error) {
      logger.error('Failed to track recommendation click', error);
    }
  }, [user]);

  /**
   * Track search to product conversion
   */
  const trackSearchToProduct = useCallback((
    productId: string,
    searchQuery: string,
    searchResults: number,
    position: number
  ) => {
    try {
      logger.userAction('search_to_product', {
        productId,
        searchQuery,
        searchResults,
        position,
        sessionId: getSessionId(),
        userId: user?.sub,
        timestamp: Date.now()
      });
    } catch (error) {
      logger.error('Failed to track search to product conversion', error);
    }
  }, [user]);

  /**
   * Track wishlist actions
   */
  const trackWishlistAction = useCallback((
    productId: string,
    action: 'add' | 'remove',
    source?: string
  ) => {
    try {
      logger.userAction('wishlist_action', {
        productId,
        action,
        source,
        sessionId: getSessionId(),
        userId: user?.sub,
        timestamp: Date.now()
      });
    } catch (error) {
      logger.error('Failed to track wishlist action', error);
    }
  }, [user]);

  /**
   * Track page view with context
   */
  const trackPageView = useCallback((
    page: string,
    context?: Record<string, any>
  ) => {
    try {
      logger.userAction('page_view', {
        page,
        context,
        sessionId: getSessionId(),
        userId: user?.sub,
        timestamp: Date.now()
      });
    } catch (error) {
      logger.error('Failed to track page view', error);
    }
  }, [user]);

  return {
    trackProductViewEnhanced,
    trackCartAdditionEnhanced,
    trackRecommendationClick,
    trackSearchToProduct,
    trackWishlistAction,
    trackPageView
  };
};
