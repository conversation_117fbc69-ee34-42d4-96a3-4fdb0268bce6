// Bundle analysis and optimization utilities
console.log('Bundle Analyzer: Initializing...');

interface BundleMetrics {
  componentName: string;
  loadTime: number;
  size?: number;
  timestamp: number;
  route?: string;
}

interface BundleAlert {
  type: 'warning' | 'error' | 'info';
  component: string;
  metric: string;
  value: number;
  threshold: number;
  message: string;
  timestamp: number;
}

// Performance thresholds for bundle analysis
const BUNDLE_THRESHOLDS = {
  componentLoadTime: { good: 100, poor: 500 }, // ms
  chunkSize: { good: 250, poor: 500 }, // KB
  totalBundleSize: { good: 1000, poor: 2000 }, // KB
  routeLoadTime: { good: 200, poor: 1000 }, // ms
};

class BundleAnalyzer {
  private metrics: BundleMetrics[] = [];
  private alerts: BundleAlert[] = [];
  private isEnabled = true;

  constructor() {
    if (typeof window !== 'undefined') {
      this.initializeTracking();
      console.log('Bundle Analyzer: Initialized successfully');
    }
  }

  private initializeTracking() {
    // Track dynamic imports and lazy loading
    this.trackDynamicImports();
    
    // Monitor route changes for bundle loading
    this.trackRouteChanges();
    
    // Analyze initial bundle size
    this.analyzeInitialBundle();
  }

  private trackDynamicImports() {
    // Override dynamic import to track loading times
    const originalImport = window.import || (() => {});
    
    // Note: This is a conceptual implementation
    // In practice, you'd integrate with your bundler's metrics
    if (typeof window !== 'undefined') {
      (window as any).trackComponentLoad = (componentName: string, loadTime: number) => {
        this.recordComponentLoad(componentName, loadTime);
      };
    }
  }

  private trackRouteChanges() {
    // Listen for route changes to track route-level bundle loading
    if (typeof window !== 'undefined') {
      let currentRoute = window.location.pathname;
      
      const observer = new MutationObserver(() => {
        if (window.location.pathname !== currentRoute) {
          const newRoute = window.location.pathname;
          this.recordRouteChange(currentRoute, newRoute);
          currentRoute = newRoute;
        }
      });
      
      observer.observe(document.body, { childList: true, subtree: true });
    }
  }

  private analyzeInitialBundle() {
    // Analyze the initial bundle size and composition
    if (typeof window !== 'undefined' && 'performance' in window) {
      const entries = performance.getEntriesByType('navigation');
      if (entries.length > 0) {
        const navigationEntry = entries[0] as PerformanceNavigationTiming;
        const loadTime = navigationEntry.loadEventEnd - navigationEntry.loadEventStart;
        
        this.recordComponentLoad('initial-bundle', loadTime);
        
        if (loadTime > BUNDLE_THRESHOLDS.routeLoadTime.poor) {
          this.createAlert('warning', 'initial-bundle', 'loadTime', loadTime, 
            BUNDLE_THRESHOLDS.routeLoadTime.poor, 
            `Initial bundle load time is slow: ${loadTime.toFixed(2)}ms`);
        }
      }
    }
  }

  private recordComponentLoad(componentName: string, loadTime: number, route?: string) {
    if (!this.isEnabled) return;

    const metric: BundleMetrics = {
      componentName,
      loadTime,
      timestamp: Date.now(),
      route: route || window.location.pathname
    };

    this.metrics.push(metric);
    
    // Keep only last 100 metrics
    if (this.metrics.length > 100) {
      this.metrics = this.metrics.slice(-100);
    }

    console.log(`Bundle: ${componentName} loaded in ${loadTime.toFixed(2)}ms`);

    // Check thresholds
    if (loadTime > BUNDLE_THRESHOLDS.componentLoadTime.poor) {
      this.createAlert('warning', componentName, 'loadTime', loadTime, 
        BUNDLE_THRESHOLDS.componentLoadTime.poor,
        `Slow component load: ${componentName} took ${loadTime.toFixed(2)}ms`);
    }
  }

  private recordRouteChange(fromRoute: string, toRoute: string) {
    const startTime = performance.now();
    
    // Use requestIdleCallback to measure when the route is fully loaded
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => {
        const loadTime = performance.now() - startTime;
        this.recordComponentLoad(`route-${toRoute}`, loadTime, toRoute);
      });
    }
  }

  private createAlert(type: BundleAlert['type'], component: string, metric: string, 
                     value: number, threshold: number, message: string) {
    const alert: BundleAlert = {
      type,
      component,
      metric,
      value,
      threshold,
      message,
      timestamp: Date.now()
    };

    this.alerts.push(alert);
    
    // Keep only last 50 alerts
    if (this.alerts.length > 50) {
      this.alerts = this.alerts.slice(-50);
    }

    // Dispatch custom event for UI components to listen to
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('bundle-alert', { detail: alert }));
    }

    console.warn(`Bundle Alert [${type}]: ${message}`);
  }

  // Public API
  public trackComponentLoad(componentName: string): () => void {
    const startTime = performance.now();
    
    return () => {
      const loadTime = performance.now() - startTime;
      this.recordComponentLoad(componentName, loadTime);
      return loadTime;
    };
  }

  public getMetrics(): BundleMetrics[] {
    return [...this.metrics];
  }

  public getAlerts(): BundleAlert[] {
    return [...this.alerts];
  }

  public getComponentStats(componentName: string) {
    const componentMetrics = this.metrics.filter(m => m.componentName === componentName);
    
    if (componentMetrics.length === 0) return null;

    const loadTimes = componentMetrics.map(m => m.loadTime);
    const avgLoadTime = loadTimes.reduce((a, b) => a + b, 0) / loadTimes.length;
    const minLoadTime = Math.min(...loadTimes);
    const maxLoadTime = Math.max(...loadTimes);

    return {
      componentName,
      loadCount: componentMetrics.length,
      avgLoadTime: Number(avgLoadTime.toFixed(2)),
      minLoadTime: Number(minLoadTime.toFixed(2)),
      maxLoadTime: Number(maxLoadTime.toFixed(2)),
      lastLoaded: componentMetrics[componentMetrics.length - 1].timestamp
    };
  }

  public getRouteStats() {
    const routeMetrics = this.metrics.filter(m => m.componentName.startsWith('route-'));
    const routeStats = new Map();

    routeMetrics.forEach(metric => {
      const route = metric.route || 'unknown';
      if (!routeStats.has(route)) {
        routeStats.set(route, []);
      }
      routeStats.get(route).push(metric.loadTime);
    });

    const result: any[] = [];
    routeStats.forEach((loadTimes, route) => {
      const avgLoadTime = loadTimes.reduce((a: number, b: number) => a + b, 0) / loadTimes.length;
      result.push({
        route,
        loadCount: loadTimes.length,
        avgLoadTime: Number(avgLoadTime.toFixed(2)),
        minLoadTime: Number(Math.min(...loadTimes).toFixed(2)),
        maxLoadTime: Number(Math.max(...loadTimes).toFixed(2))
      });
    });

    return result.sort((a, b) => b.avgLoadTime - a.avgLoadTime);
  }

  public getSummary() {
    if (this.metrics.length === 0) return null;

    const totalComponents = new Set(this.metrics.map(m => m.componentName)).size;
    const avgLoadTime = this.metrics.reduce((sum, m) => sum + m.loadTime, 0) / this.metrics.length;
    
    return {
      totalComponents,
      totalLoads: this.metrics.length,
      avgLoadTime: Number(avgLoadTime.toFixed(2)),
      alertsCount: {
        error: this.alerts.filter(a => a.type === 'error').length,
        warning: this.alerts.filter(a => a.type === 'warning').length,
        info: this.alerts.filter(a => a.type === 'info').length,
      }
    };
  }

  public clearMetrics() {
    this.metrics = [];
    this.alerts = [];
  }

  public enable() {
    this.isEnabled = true;
  }

  public disable() {
    this.isEnabled = false;
  }
}

// Create singleton instance
const bundleAnalyzer = new BundleAnalyzer();

// Export convenient functions
export const trackComponentLoad = (componentName: string) => 
  bundleAnalyzer.trackComponentLoad(componentName);

export const getBundleMetrics = () => bundleAnalyzer.getMetrics();
export const getBundleAlerts = () => bundleAnalyzer.getAlerts();
export const getComponentStats = (componentName: string) => bundleAnalyzer.getComponentStats(componentName);
export const getRouteStats = () => bundleAnalyzer.getRouteStats();
export const getBundleSummary = () => bundleAnalyzer.getSummary();
export const clearBundleMetrics = () => bundleAnalyzer.clearMetrics();

export default bundleAnalyzer;
