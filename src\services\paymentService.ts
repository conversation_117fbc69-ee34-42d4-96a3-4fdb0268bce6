import { loadStripe, Stripe } from '@stripe/stripe-js';
import { Order } from '@/types/order';
import { toast } from 'sonner';

// Initialize Stripe with publishable key from environment variable
const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY);

// Create a payment intent for Stripe
export const createPaymentIntent = async (amount: number, currency: string = 'usd'): Promise<{ clientSecret: string } | null> => {
  try {
    // In a production environment, this would be a secure API call to your backend
    // For development purposes, we're creating a simple endpoint

    // Create the request to our API endpoint
    const response = await fetch('/api/create-payment-intent', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        amount: Math.round(amount * 100), // Convert to cents
        currency,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to create payment intent');
    }

    const data = await response.json();
    return { clientSecret: data.clientSecret };
  } catch (error) {
    console.error('Error creating payment intent:', error);
    toast.error('Failed to create payment intent');

    // For development fallback when API isn't available
    if (import.meta.env.DEV) {
      console.warn('Using development fallback for payment intent');
      const fakeClientSecret = `pi_${Math.random().toString(36).substring(2)}_secret_${Math.random().toString(36).substring(2)}`;
      return { clientSecret: fakeClientSecret };
    }

    return null;
  }
};

// Confirm a payment intent
export const confirmPayment = async (
  stripe: Stripe | null,
  clientSecret: string,
  paymentMethodId: string
): Promise<{ success: boolean; error?: string; paymentIntentId?: string }> => {
  if (!stripe) {
    return { success: false, error: 'Stripe not initialized' };
  }

  try {
    // Confirm the card payment with the provided client secret and payment method
    const { error, paymentIntent } = await stripe.confirmCardPayment(clientSecret, {
      payment_method: paymentMethodId
    });

    if (error) {
      console.error('Payment confirmation error:', error);
      return {
        success: false,
        error: error.message || 'Payment failed'
      };
    }

    if (paymentIntent?.status === 'succeeded') {
      // Payment succeeded, return the payment intent ID for order tracking
      return {
        success: true,
        paymentIntentId: paymentIntent.id
      };
    } else if (paymentIntent?.status === 'requires_action') {
      // Additional authentication required (e.g., 3D Secure)
      return {
        success: false,
        error: 'Additional authentication required. Please complete the verification.'
      };
    } else {
      // Other payment statuses (processing, requires_capture, etc.)
      return {
        success: false,
        error: `Payment status: ${paymentIntent?.status}. Please try again or contact support.`
      };
    }
  } catch (error) {
    console.error('Error confirming payment:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unexpected error occurred'
    };
  }
};

// Create a PayPal order
export const createPayPalOrder = async (
  amount: number,
  currency: string = 'USD'
): Promise<{ orderId: string; status: string; links: any[] }> => {
  try {
    // Create the request to our API endpoint
    const response = await fetch('/api/create-paypal-order', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        amount,
        currency,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create PayPal order');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error creating PayPal order:', error);
    toast.error('Failed to create PayPal order');

    // For development fallback when API isn't available
    if (import.meta.env.DEV) {
      console.warn('Using development fallback for PayPal order');
      return {
        orderId: `PAYPAL-${Math.random().toString(36).substring(2).toUpperCase()}`,
        status: 'CREATED',
        links: []
      };
    }

    throw error;
  }
};

// Capture a PayPal payment
export const capturePayPalPayment = async (
  orderId: string
): Promise<{ status: string; paymentId: string; payerId: string; captureId: string }> => {
  try {
    // Create the request to our API endpoint
    const response = await fetch('/api/capture-paypal-payment', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        orderId,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to capture PayPal payment');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error capturing PayPal payment:', error);
    toast.error('Failed to capture PayPal payment');

    // For development fallback when API isn't available
    if (import.meta.env.DEV) {
      console.warn('Using development fallback for PayPal capture');
      return {
        status: 'COMPLETED',
        paymentId: `PAYPAL-PAYMENT-${Math.random().toString(36).substring(2).toUpperCase()}`,
        payerId: `PAYER-${Math.random().toString(36).substring(2).toUpperCase()}`,
        captureId: `CAPTURE-${Math.random().toString(36).substring(2).toUpperCase()}`
      };
    }

    throw error;
  }
};

// Generate a receipt for an order
export const generateReceipt = async (order: Order): Promise<{ url: string } | null> => {
  try {
    // In a real application, this would be an API call to your backend
    // which would generate a PDF receipt and return a URL to download it

    // For demo purposes, we'll simulate a successful response with a fake URL
    const fakeReceiptUrl = `/receipts/order-${order.id}.pdf`;

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 600));

    return { url: fakeReceiptUrl };
  } catch (error) {
    console.error('Error generating receipt:', error);
    toast.error('Failed to generate receipt');
    return null;
  }
};

// Process a refund
export const processRefund = async (
  orderId: string,
  amount: number,
  reason: string
): Promise<{ success: boolean; refundId?: string; error?: string }> => {
  try {
    // In a real application, this would be an API call to your backend
    // which would then call the payment provider's API to process the refund

    // For demo purposes, we'll simulate a successful response
    const fakeRefundId = `REF-${Math.random().toString(36).substring(2).toUpperCase()}`;

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 700));

    return { success: true, refundId: fakeRefundId };
  } catch (error) {
    console.error('Error processing refund:', error);
    return { success: false, error: 'Failed to process refund' };
  }
};
