import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";
import { useReviews } from "@/contexts/ReviewContext";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { StarRating } from "@/components/ui/star-rating";
import { Product } from "@/data/products";

// Form validation schema
const reviewFormSchema = z.object({
  rating: z.number().min(1, { message: "Please select a rating" }).max(5),
  title: z.string().min(3, { message: "Title must be at least 3 characters" }).max(100),
  content: z.string().min(10, { message: "Review must be at least 10 characters" }).max(1000),
  images: z.array(z.string()).optional(),
});

type ReviewFormValues = z.infer<typeof reviewFormSchema>;

interface ReviewFormProps {
  product: Product;
  onSuccess?: () => void;
}

const ReviewForm = ({ product, onSuccess }: ReviewFormProps) => {
  const { user, isAuthenticated, login } = useAuth();
  const { submitReview, uploadReviewImages, isVerifiedPurchaser } = useReviews();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);
  const [isVerified, setIsVerified] = useState(false);

  const form = useForm<ReviewFormValues>({
    resolver: zodResolver(reviewFormSchema),
    defaultValues: {
      rating: 0,
      title: "",
      content: "",
      images: [],
    },
  });

  // Check if user is a verified purchaser when component mounts
  React.useEffect(() => {
    if (isAuthenticated && user) {
      const verified = isVerifiedPurchaser(product.id);
      setIsVerified(verified);
    }
  }, [isAuthenticated, user, product.id, isVerifiedPurchaser]);

  const onSubmit = async (data: ReviewFormValues) => {
    if (!isAuthenticated || !user) {
      toast.error("Please log in to submit a review");
      return;
    }

    setIsSubmitting(true);

    try {
      await submitReview({
        productId: product.id,
        rating: data.rating,
        title: data.title,
        content: data.content,
        images: uploadedImages,
      });

      form.reset();
      setUploadedImages([]);

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error("Error submitting review:", error);
      toast.error("Failed to submit review. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle image upload
  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    try {
      // Convert FileList to array
      const fileArray = Array.from(files);

      // Limit to 3 images total (including existing ones)
      const filesToUpload = fileArray.slice(0, 3 - uploadedImages.length);

      if (filesToUpload.length > 0) {
        // Upload the images
        const newImageUrls = await uploadReviewImages(filesToUpload);

        // Update state with new images
        const updatedImages = [...uploadedImages, ...newImageUrls].slice(0, 3);
        setUploadedImages(updatedImages);
        form.setValue('images', updatedImages);
      }
    } catch (error) {
      console.error("Error uploading images:", error);
      toast.error("Failed to upload images. Please try again.");
    }
  };

  const removeImage = (index: number) => {
    const updatedImages = [...uploadedImages];
    updatedImages.splice(index, 1);
    setUploadedImages(updatedImages);
    form.setValue('images', updatedImages);
  };

  if (!isAuthenticated) {
    return (
      <div className="bg-sabone-dark-olive/40 p-6 rounded-lg gold-border text-center">
        <p className="text-sabone-cream mb-4">Please log in to write a review</p>
        <Button
          className="bg-sabone-gold hover:bg-sabone-gold/80 text-sabone-charcoal font-medium"
          onClick={login}
        >
          Log In
        </Button>
      </div>
    );
  }

  return (
    <div className="bg-sabone-dark-olive/40 p-6 rounded-lg gold-border">
      <h3 className="text-xl font-playfair font-semibold text-sabone-gold mb-4">
        Write a Review
      </h3>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="rating"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sabone-cream">Rating</FormLabel>
                <FormControl>
                  <StarRating
                    rating={field.value}
                    interactive
                    size="lg"
                    onChange={field.onChange}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="title"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sabone-cream">Review Title</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="Summarize your experience"
                    className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="content"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sabone-cream">Your Review</FormLabel>
                <FormControl>
                  <Textarea
                    {...field}
                    placeholder="Share your experience with this product"
                    className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream min-h-[120px]"
                  />
                </FormControl>
                <p className="text-xs text-sabone-cream/70 mt-1">
                  {field.value.length}/1000 characters
                </p>
                <FormMessage />
              </FormItem>
            )}
          />

          <div>
            <FormLabel className="text-sabone-cream">Add Photos (Optional)</FormLabel>
            <div className="mt-2">
              <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed border-sabone-gold/30 rounded-md cursor-pointer bg-sabone-charcoal/50 hover:bg-sabone-charcoal transition-colors">
                <div className="flex flex-col items-center justify-center pt-5 pb-6">
                  <p className="text-sm text-sabone-cream/70">
                    <span className="font-medium text-sabone-gold">Click to upload</span> or drag and drop
                  </p>
                  <p className="text-xs text-sabone-cream/70 mt-1">
                    PNG, JPG or WEBP (max 3 images)
                  </p>
                </div>
                <input
                  type="file"
                  className="hidden"
                  accept="image/*"
                  multiple
                  onChange={handleImageUpload}
                  disabled={uploadedImages.length >= 3}
                />
              </label>
            </div>

            {uploadedImages.length > 0 && (
              <div className="mt-4 flex flex-wrap gap-2">
                {uploadedImages.map((image, index) => (
                  <div key={index} className="relative w-20 h-20">
                    <img
                      src={image}
                      alt={`Uploaded ${index + 1}`}
                      className="w-full h-full object-cover rounded-md"
                    />
                    <button
                      type="button"
                      className="absolute -top-2 -right-2 bg-sabone-charcoal text-sabone-gold rounded-full p-1"
                      onClick={() => removeImage(index)}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                      </svg>
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          <Button
            type="submit"
            className="bg-sabone-gold hover:bg-sabone-gold/80 text-sabone-charcoal font-medium"
            disabled={isSubmitting}
          >
            {isSubmitting ? "Submitting..." : "Submit Review"}
          </Button>
        </form>
      </Form>
    </div>
  );
};

export default ReviewForm;
