import { useState, useEffect, useRef, useCallback } from "react";
import { Menu, Search, ShoppingCart, Heart, LogIn, User, X } from "lucide-react";
import { Sheet, Sheet<PERSON>ontent, SheetHeader, SheetTitle } from "@/components/ui/sheet";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Link, useLocation } from "react-router-dom";
import { useCart } from "@/contexts/CartContext";
import { useAuth } from "@/contexts/AuthContext";
import { useWishlist } from "@/contexts/WishlistContext";
import { useTranslations } from "next-intl";
import { SimpleLanguageSwitcher } from "@/components/i18n/LanguageSwitcher";
import DirectLoginButton from "@/components/auth/DirectLoginButton";
import useMobile from "@/hooks/useMobile";
import { useMobileOptimization } from "@/hooks/useMobileOptimization";

interface MobileNavigationProps {
  isScrolled: boolean;
  onSignInClick: () => void;
  onSignUpClick: () => void;
}

const MobileNavigation = ({ isScrolled, onSignInClick, onSignUpClick }: MobileNavigationProps) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isMenuAnimating, setIsMenuAnimating] = useState(false);
  const { itemCount } = useCart();
  const { wishlistCount } = useWishlist();
  const { isAuthenticated, user, logout } = useAuth();
  const location = useLocation();
  const tCommon = useTranslations('common');
  const tAccount = useTranslations('account');
  const { isMobile, isTouchDevice } = useMobile();

  // Mobile optimization hooks
  const {
    triggerHapticFeedback,
    setupTouchEvents,
    preventZoom,
    shouldReduceAnimations,
    currentGesture,
  } = useMobileOptimization({
    enableTouchGestures: true,
    enableHapticFeedback: true,
  });

  // Refs for touch optimization
  const menuButtonRef = useRef<HTMLButtonElement>(null);
  const menuContentRef = useRef<HTMLDivElement>(null);

  // Helper function to safely get translations with fallback
  const getTranslation = (translationFn: (key: string) => string, key: string, fallback: string) => {
    try {
      const translation = translationFn(key);
      // If translation returns the key itself, it means translation failed
      return translation === key ? fallback : translation;
    } catch (error) {
      console.warn(`Translation failed for key: ${key}`, error);
      return fallback;
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase();
  };

  // Enhanced mobile menu handlers with haptic feedback
  const handleMenuToggle = useCallback(() => {
    triggerHapticFeedback('light');
    setIsMenuAnimating(true);
    setIsMobileMenuOpen(!isMobileMenuOpen);

    setTimeout(() => setIsMenuAnimating(false), shouldReduceAnimations ? 100 : 300);
  }, [isMobileMenuOpen, triggerHapticFeedback, shouldReduceAnimations]);

  const scrollToSection = useCallback((id: string) => {
    triggerHapticFeedback('light');
    if (location.pathname === '/') {
      const element = document.getElementById(id);
      if (element) {
        element.scrollIntoView({
          behavior: shouldReduceAnimations ? "auto" : "smooth",
          block: "start"
        });
      }
    } else {
      window.location.href = `/#${id}`;
    }
    setIsMobileMenuOpen(false);
  }, [location.pathname, triggerHapticFeedback, shouldReduceAnimations]);

  const handleMenuItemClick = useCallback((action: () => void) => {
    triggerHapticFeedback('light');
    setIsMobileMenuOpen(false);
    // Delay based on animation preference
    const delay = shouldReduceAnimations ? 50 : 150;
    setTimeout(action, delay);
  }, [triggerHapticFeedback, shouldReduceAnimations]);

  // Setup touch events for menu elements
  useEffect(() => {
    if (!menuButtonRef.current) return;

    const cleanupButton = setupTouchEvents(menuButtonRef.current);
    const cleanupPreventZoom = preventZoom(menuButtonRef.current);

    return () => {
      cleanupButton();
      cleanupPreventZoom();
    };
  }, [setupTouchEvents, preventZoom]);

  // Handle swipe gestures to close menu
  useEffect(() => {
    if (currentGesture?.type === 'swipe' && currentGesture.direction === 'left' && isMobileMenuOpen) {
      setIsMobileMenuOpen(false);
      triggerHapticFeedback('light');
    }
  }, [currentGesture, isMobileMenuOpen, triggerHapticFeedback]);

  if (!isMobile) return null;

  return (
    <>
      {/* Mobile Header */}
      <div className="flex justify-between items-center h-16 w-full md:hidden">
        {/* Enhanced Menu Button with Touch Optimization */}
        <button
          ref={menuButtonRef}
          type="button"
          className={`text-sabone-gold mr-2 p-3 rounded-full hover:bg-sabone-charcoal-deep/30 transition-all duration-200 min-h-[44px] min-w-[44px] flex items-center justify-center active:scale-95 ${
            isMenuAnimating ? 'pointer-events-none' : ''
          } ${shouldReduceAnimations ? 'transition-none' : ''}`}
          onClick={handleMenuToggle}
          aria-label={isMobileMenuOpen ? "Close menu" : "Open menu"}
          aria-expanded={isMobileMenuOpen ? "true" : "false"}
          disabled={isMenuAnimating}
        >
          <div className={`transition-transform duration-200 ${isMobileMenuOpen ? 'rotate-90' : 'rotate-0'} ${shouldReduceAnimations ? 'transition-none' : ''}`}>
            {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </div>
          <span className="absolute inset-0 rounded-full bg-sabone-gold/5 opacity-0 hover:opacity-100 transition-opacity duration-300"></span>
        </button>

        {/* Logo */}
        <div className="h-8">
          <Link to="/">
            <img
              src="/lovable-uploads/f29136a5-d354-4160-9b44-ef12ca8c6b3d.png"
              alt="Sabone Logo"
              className="h-full w-auto"
            />
          </Link>
        </div>

        {/* Mobile Actions */}
        <div className="flex items-center space-x-2">
          {/* Search Button */}
          <Link
            to="/search"
            className="text-sabone-cream hover:text-sabone-gold-accent transition-all duration-200 relative group p-2 rounded-full hover:bg-sabone-charcoal-deep/30 active:scale-95 inline-block"
            aria-label="Search products"
          >
            <Search size={18} />
            <span className="absolute inset-0 rounded-full bg-sabone-gold/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
          </Link>

          {/* Wishlist Button */}
          <div className="relative">
            <Link
              to="/wishlist"
              className="text-sabone-cream hover:text-sabone-gold-accent transition-all duration-200 inline-block relative group p-2 rounded-full hover:bg-sabone-charcoal-deep/30 active:scale-95"
              aria-label="View wishlist"
            >
              <Heart size={18} />
              <span className="absolute inset-0 rounded-full bg-sabone-gold/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
              {wishlistCount > 0 && (
                <Badge className="absolute -top-1 -right-1 bg-sabone-gold-rich text-sabone-charcoal-deep h-5 w-5 flex items-center justify-center p-0 text-xs font-medium shadow-sm animate-soft-glow">
                  {wishlistCount}
                </Badge>
              )}
            </Link>
          </div>

          {/* Cart Button */}
          <div className="relative">
            <Link
              to="/checkout"
              className="text-sabone-cream hover:text-sabone-gold-accent transition-all duration-200 inline-block relative group p-2 rounded-full hover:bg-sabone-charcoal-deep/30 active:scale-95"
              aria-label="View cart"
            >
              <ShoppingCart size={18} />
              <span className="absolute inset-0 rounded-full bg-sabone-gold/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
              {itemCount > 0 && (
                <Badge className="absolute -top-1 -right-1 bg-sabone-gold-rich text-sabone-charcoal-deep h-5 w-5 flex items-center justify-center p-0 text-xs font-medium shadow-sm animate-soft-glow">
                  {itemCount}
                </Badge>
              )}
            </Link>
          </div>

          {/* User Avatar or Login */}
          {isAuthenticated ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-8 w-8 rounded-full p-0 overflow-hidden group">
                  <Avatar className="h-8 w-8 border border-sabone-gold/30 transition-all duration-300 group-hover:border-sabone-gold/60 group-hover:shadow-[0_0_10px_rgba(198,168,112,0.2)]">
                    <AvatarImage src={user?.picture} alt={user?.name} />
                    <AvatarFallback className="bg-sabone-gold/20 text-sabone-gold-rich text-xs font-medium">
                      {user?.name ? getInitials(user.name) : 'U'}
                    </AvatarFallback>
                  </Avatar>
                  <span className="absolute inset-0 rounded-full bg-sabone-gold/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-56 bg-sabone-charcoal-deep/95 backdrop-blur-[4px] border-sabone-gold/30 shadow-[0_5px_15px_rgba(0,0,0,0.3)] z-[100]"
                align="end"
                forceMount
                sideOffset={8}
              >
                <DropdownMenuLabel className="text-sabone-gold-accent font-medium">
                  {getTranslation(tAccount, 'title', 'My Account')}
                </DropdownMenuLabel>
                <DropdownMenuSeparator className="bg-sabone-gold/20" />
                <DropdownMenuItem className="text-sabone-cream hover:text-sabone-gold-accent hover:bg-sabone-gold/10 transition-all duration-200 focus:bg-sabone-gold/15" asChild>
                  <Link to="/account">{getTranslation(tAccount, 'profile', 'Profile')}</Link>
                </DropdownMenuItem>
                <DropdownMenuItem className="text-sabone-cream hover:text-sabone-gold-accent hover:bg-sabone-gold/10 transition-all duration-200 focus:bg-sabone-gold/15" asChild>
                  <Link to="/account/orders">{getTranslation(tAccount, 'orders', 'Orders')}</Link>
                </DropdownMenuItem>
                <DropdownMenuItem className="text-sabone-cream hover:text-sabone-gold-accent hover:bg-sabone-gold/10 transition-all duration-200 focus:bg-sabone-gold/15" asChild>
                  <Link to="/account/addresses">{getTranslation(tAccount, 'addresses', 'Addresses')}</Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator className="bg-sabone-gold/20" />
                <DropdownMenuItem
                  className="text-sabone-cream hover:text-sabone-gold-accent hover:bg-sabone-gold/10 transition-all duration-200 focus:bg-sabone-gold/15"
                  onClick={() => logout(() => onSignInClick())}
                >
                  {getTranslation(tAccount, 'logout', 'Logout')}
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <Button
              variant="ghost"
              size="icon"
              className="text-sabone-cream hover:text-sabone-gold-accent transition-all duration-200 relative group p-2 rounded-full hover:bg-sabone-charcoal-deep/30 active:scale-95"
              onClick={onSignInClick}
            >
              <LogIn size={18} />
              <span className="absolute inset-0 rounded-full bg-sabone-gold/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
            </Button>
          )}
        </div>
      </div>

      {/* Mobile Menu Sheet */}
      <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
        <SheetContent
          side="left"
          className="bg-sabone-charcoal-deep/95 border-sabone-gold/20 backdrop-blur-[5px] shadow-[5px_0_25px_rgba(0,0,0,0.3)] w-80"
        >
          <SheetHeader>
            <SheetTitle className="text-sabone-gold-accent font-playfair text-2xl">Menu</SheetTitle>
          </SheetHeader>

          {/* Enhanced visual effects for mobile */}
          <div className="absolute inset-0 bg-texture-overlay opacity-5 mix-blend-overlay pointer-events-none"></div>
          <div className="absolute top-1/4 right-1/4 w-64 h-64 rounded-full bg-sabone-gold/5 blur-3xl opacity-30 pointer-events-none"></div>

          <div className="py-8 relative z-10">
            <nav className="flex flex-col space-y-6">
              {/* Navigation Links with Enhanced Mobile Interactions */}
              <Link
                to="/search"
                onClick={() => setIsMobileMenuOpen(false)}
                className="text-sabone-cream hover:text-sabone-gold-accent transition-all duration-300 text-lg font-medium flex items-center group active:scale-95 min-h-[48px]"
              >
                <span className="w-0 h-[1px] bg-sabone-gold-accent mr-0 group-hover:w-4 group-hover:mr-3 transition-all duration-300"></span>
                <span className="flex items-center">
                  <Search size={18} className="mr-2" />
                  Search Products
                </span>
              </Link>

              <button
                type="button"
                onClick={() => handleMenuItemClick(() => scrollToSection("products-section"))}
                className="text-sabone-cream hover:text-sabone-gold-accent transition-all duration-300 text-lg font-medium flex items-center group active:scale-95 min-h-[48px]"
              >
                <span className="w-0 h-[1px] bg-sabone-gold-accent mr-0 group-hover:w-4 group-hover:mr-3 transition-all duration-300"></span>
                <span>{getTranslation(tCommon, 'navigation.shop', 'Shop')}</span>
              </button>

              <button
                type="button"
                onClick={() => handleMenuItemClick(() => scrollToSection("about-section"))}
                className="text-sabone-cream hover:text-sabone-gold-accent transition-all duration-300 text-lg font-medium flex items-center group active:scale-95 min-h-[48px]"
              >
                <span className="w-0 h-[1px] bg-sabone-gold-accent mr-0 group-hover:w-4 group-hover:mr-3 transition-all duration-300"></span>
                <span>{getTranslation(tCommon, 'navigation.about', 'About')}</span>
              </button>

              <button
                type="button"
                onClick={() => handleMenuItemClick(() => scrollToSection("contact-section"))}
                className="text-sabone-cream hover:text-sabone-gold-accent transition-all duration-300 text-lg font-medium flex items-center group active:scale-95 min-h-[48px]"
              >
                <span className="w-0 h-[1px] bg-sabone-gold-accent mr-0 group-hover:w-4 group-hover:mr-3 transition-all duration-300"></span>
                <span>{getTranslation(tCommon, 'navigation.contact', 'Contact')}</span>
              </button>

              <Link
                to="/localization"
                onClick={() => setIsMobileMenuOpen(false)}
                className="text-sabone-cream hover:text-sabone-gold-accent transition-all duration-300 text-lg font-medium flex items-center group active:scale-95 min-h-[48px]"
              >
                <span className="w-0 h-[1px] bg-sabone-gold-accent mr-0 group-hover:w-4 group-hover:mr-3 transition-all duration-300"></span>
                <span className="relative">
                  Localization Demo
                  <span className="ml-2 text-xs px-1.5 py-0.5 bg-sabone-gold/20 text-sabone-gold rounded-full">New</span>
                </span>
              </Link>

              {/* Authentication Links */}
              {isAuthenticated ? (
                <>
                  <Link
                    to="/account"
                    onClick={() => setIsMobileMenuOpen(false)}
                    className="text-sabone-cream hover:text-sabone-gold-accent transition-all duration-300 text-lg font-medium flex items-center group active:scale-95 min-h-[48px]"
                  >
                    <span className="w-0 h-[1px] bg-sabone-gold-accent mr-0 group-hover:w-4 group-hover:mr-3 transition-all duration-300"></span>
                    <span>{getTranslation(tCommon, 'navigation.account', 'Account')}</span>
                  </Link>

                  <Link
                    to="/wishlist"
                    onClick={() => setIsMobileMenuOpen(false)}
                    className="text-sabone-cream hover:text-sabone-gold-accent transition-all duration-300 text-lg font-medium flex items-center group active:scale-95 min-h-[48px]"
                  >
                    <span className="w-0 h-[1px] bg-sabone-gold-accent mr-0 group-hover:w-4 group-hover:mr-3 transition-all duration-300"></span>
                    <span className="flex items-center">
                      {getTranslation(tCommon, 'navigation.wishlist', 'Wishlist')}
                      {wishlistCount > 0 && (
                        <Badge className="ml-2 bg-sabone-gold text-sabone-charcoal-deep">
                          {wishlistCount}
                        </Badge>
                      )}
                    </span>
                  </Link>
                </>
              ) : (
                <div className="flex flex-col space-y-4">
                  <button
                    type="button"
                    onClick={() => handleMenuItemClick(onSignInClick)}
                    className="text-sabone-cream hover:text-sabone-gold-accent transition-all duration-300 text-lg font-medium flex items-center group active:scale-95 min-h-[48px]"
                  >
                    <span className="w-0 h-[1px] bg-sabone-gold-accent mr-0 group-hover:w-4 group-hover:mr-3 transition-all duration-300"></span>
                    <span>{getTranslation(tCommon, 'buttons.signIn', 'Sign In')}</span>
                  </button>

                  <button
                    type="button"
                    onClick={() => handleMenuItemClick(onSignUpClick)}
                    className="text-sabone-cream hover:text-sabone-gold-accent transition-all duration-300 text-lg font-medium flex items-center group active:scale-95 min-h-[48px]"
                  >
                    <span className="w-0 h-[1px] bg-sabone-gold-accent mr-0 group-hover:w-4 group-hover:mr-3 transition-all duration-300"></span>
                    <span>{getTranslation(tCommon, 'buttons.signUp', 'Sign Up')}</span>
                  </button>
                </div>
              )}
            </nav>

            {/* Direct Login Button for Mobile */}
            {!isAuthenticated && (
              <div className="mt-6">
                <DirectLoginButton className="w-full" />
              </div>
            )}

            {/* Language Switcher */}
            <div className="mt-6 mb-4">
              <div className="text-sabone-cream text-sm uppercase tracking-wide font-medium mb-2">
                {getTranslation(tCommon, 'languageSwitcher.label', 'Change Language')}
              </div>
              <div className="flex justify-start">
                <SimpleLanguageSwitcher />
              </div>
            </div>

            {/* Decorative element */}
            <div className="absolute bottom-12 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-sabone-gold/20 to-transparent"></div>
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
};

export default MobileNavigation;