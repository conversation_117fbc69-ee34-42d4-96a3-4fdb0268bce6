import { ReactNode, useEffect, useState } from 'react';
import { PayPalScriptProvider } from '@paypal/react-paypal-js';

interface PayPalProviderProps {
  children: ReactNode;
}

const PayPalProvider = ({ children }: PayPalProviderProps) => {
  const [mounted, setMounted] = useState(false);

  // Ensure component is mounted before rendering PayPal
  // This helps prevent hydration issues
  useEffect(() => {
    setMounted(true);
  }, []);

  // Get PayPal client ID from environment variables
  const paypalClientId = import.meta.env.VITE_PAYPAL_CLIENT_ID || '';
  const isSandbox = import.meta.env.VITE_PAYPAL_SANDBOX === 'true';

  // Enhanced options for PayPal with luxury styling
  const paypalOptions = {
    'client-id': paypalClientId,
    currency: 'USD',
    intent: 'capture',
    // Use sandbox for development, production for live
    'data-client-token': 'abc123xyz', // This would be a real client token in production
    components: 'buttons',
    'disable-funding': 'credit,card', // Disable specific funding sources if needed
    'enable-funding': 'venmo,paylater', // Enable specific funding sources if needed
    'data-namespace': 'sabone_paypal',
  };

  if (!mounted) {
    return null;
  }

  return (
    <PayPalScriptProvider options={paypalOptions}>
      {children}
    </PayPalScriptProvider>
  );
};

export default PayPalProvider;
