import { useEffect, useCallback, useState } from 'react';

interface PerformanceData {
  renderTime: number;
  componentName: string;
  timestamp: number;
}

interface PerformanceHookResult {
  startTracking: () => void;
  stopTracking: () => number;
  performanceData: PerformanceData[];
  clearData: () => void;
  getAverageRenderTime: () => number;
  getSlowRenders: () => PerformanceData[];
}

const SLOW_RENDER_THRESHOLD = 16; // 60fps = 16.67ms per frame

export const usePerformance = (componentName: string): PerformanceHookResult => {
  const [performanceData, setPerformanceData] = useState<PerformanceData[]>([]);
  const [startTime, setStartTime] = useState<number | null>(null);

  const startTracking = useCallback(() => {
    setStartTime(performance.now());
  }, []);

  const stopTracking = useCallback(() => {
    if (startTime === null) return 0;
    
    const endTime = performance.now();
    const renderTime = endTime - startTime;
    
    const newData: PerformanceData = {
      renderTime,
      componentName,
      timestamp: Date.now()
    };

    setPerformanceData(prev => {
      const updated = [...prev, newData];
      // Keep only the last 50 measurements
      return updated.length > 50 ? updated.slice(-50) : updated;
    });

    // Log slow renders
    if (renderTime > SLOW_RENDER_THRESHOLD) {
      console.warn(`Slow render detected in ${componentName}: ${renderTime.toFixed(2)}ms`);
    }

    setStartTime(null);
    return renderTime;
  }, [startTime, componentName]);

  const clearData = useCallback(() => {
    setPerformanceData([]);
  }, []);

  const getAverageRenderTime = useCallback(() => {
    if (performanceData.length === 0) return 0;
    const total = performanceData.reduce((sum, data) => sum + data.renderTime, 0);
    return total / performanceData.length;
  }, [performanceData]);

  const getSlowRenders = useCallback(() => {
    return performanceData.filter(data => data.renderTime > SLOW_RENDER_THRESHOLD);
  }, [performanceData]);

  // Auto-start tracking on mount and stop on unmount
  useEffect(() => {
    startTracking();
    return () => {
      if (startTime !== null) {
        stopTracking();
      }
    };
  }, []);

  return {
    startTracking,
    stopTracking,
    performanceData,
    clearData,
    getAverageRenderTime,
    getSlowRenders
  };
};

// Global performance utilities
export const measureAsync = async <T>(name: string, fn: () => Promise<T>): Promise<T> => {
  const start = performance.now();
  try {
    const result = await fn();
    const duration = performance.now() - start;
    console.log(`${name} completed in ${duration.toFixed(2)}ms`);
    return result;
  } catch (error) {
    const duration = performance.now() - start;
    console.error(`${name} failed after ${duration.toFixed(2)}ms:`, error);
    throw error;
  }
};

export const measureSync = <T>(name: string, fn: () => T): T => {
  const start = performance.now();
  try {
    const result = fn();
    const duration = performance.now() - start;
    console.log(`${name} completed in ${duration.toFixed(2)}ms`);
    return result;
  } catch (error) {
    const duration = performance.now() - start;
    console.error(`${name} failed after ${duration.toFixed(2)}ms:`, error);
    throw error;
  }
};

// Web Vitals tracking utility
export const trackWebVitals = () => {
  if (typeof window === 'undefined') return;

  // Track LCP
  if ('PerformanceObserver' in window) {
    try {
      new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        console.log(`LCP: ${lastEntry.startTime.toFixed(2)}ms`);
      }).observe({ type: 'largest-contentful-paint', buffered: true });
    } catch (e) {
      console.warn('LCP tracking not supported');
    }

    // Track FID
    try {
      new PerformanceObserver((list) => {
        list.getEntries().forEach((entry: any) => {
          const fid = entry.processingStart - entry.startTime;
          console.log(`FID: ${fid.toFixed(2)}ms`);
        });
      }).observe({ type: 'first-input', buffered: true });
    } catch (e) {
      console.warn('FID tracking not supported');
    }

    // Track CLS
    try {
      let clsValue = 0;
      new PerformanceObserver((list) => {
        list.getEntries().forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });
        console.log(`CLS: ${clsValue.toFixed(4)}`);
      }).observe({ type: 'layout-shift', buffered: true });
    } catch (e) {
      console.warn('CLS tracking not supported');
    }
  }
};

export default usePerformance; 