import { useState, useRef, useEffect } from "react";
import { Product } from "@/data/products";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { useReviews } from "@/contexts/ReviewContext";
import ReviewSummary from "./ReviewSummary";
import ReviewList from "./ReviewList";
import ReviewForm from "./ReviewForm";

interface ReviewSectionProps {
  product: Product;
}

const ReviewSection = ({ product }: ReviewSectionProps) => {
  const { refreshReviews } = useReviews();
  const [activeTab, setActiveTab] = useState<string>("reviews");
  const reviewFormRef = useRef<HTMLDivElement>(null);

  // Load reviews when component mounts
  useEffect(() => {
    refreshReviews(product.id);
  }, [product.id, refreshReviews]);

  const handleWriteReviewClick = () => {
    setActiveTab("write");
    // Scroll to the review form
    setTimeout(() => {
      reviewFormRef.current?.scrollIntoView({ behavior: "smooth" });
    }, 100);
  };

  const handleReviewSubmitted = () => {
    // Refresh reviews after submission
    refreshReviews(product.id);
    // Switch back to reviews tab after successful submission
    setActiveTab("reviews");
  };

  return (
    <section className="mt-16">
      <h2 className="text-2xl font-playfair font-bold text-sabone-gold mb-8">
        Customer Reviews
      </h2>

      <ReviewSummary
        productId={product.id}
        onWriteReviewClick={handleWriteReviewClick}
      />

      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="mt-8"
      >
        <TabsList className="bg-sabone-dark-olive/60 border-b border-sabone-gold/20 w-full justify-start rounded-none p-0">
          <TabsTrigger
            value="reviews"
            className="rounded-none border-b-2 border-transparent data-[state=active]:border-sabone-gold data-[state=active]:bg-transparent data-[state=active]:text-sabone-gold py-3 px-6 text-sabone-cream/70 hover:text-sabone-cream transition-colors"
          >
            All Reviews
          </TabsTrigger>
          <TabsTrigger
            value="write"
            className="rounded-none border-b-2 border-transparent data-[state=active]:border-sabone-gold data-[state=active]:bg-transparent data-[state=active]:text-sabone-gold py-3 px-6 text-sabone-cream/70 hover:text-sabone-cream transition-colors"
          >
            Write a Review
          </TabsTrigger>
        </TabsList>

        <TabsContent value="reviews" className="mt-6">
          <ReviewList productId={product.id} />
        </TabsContent>

        <TabsContent value="write" className="mt-6">
          <div ref={reviewFormRef}>
            <ReviewForm product={product} onSuccess={handleReviewSubmitted} />
          </div>
        </TabsContent>
      </Tabs>
    </section>
  );
};

export default ReviewSection;
