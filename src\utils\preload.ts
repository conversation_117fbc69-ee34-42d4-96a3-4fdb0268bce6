/**
 * Utility functions for preloading resources
 */

/**
 * Preload an image
 * @param src Image URL to preload
 * @returns Promise that resolves when the image is loaded
 */
export function preloadImage(src: string): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = reject;
    img.src = src;
  });
}

/**
 * Preload multiple images
 * @param srcs Array of image URLs to preload
 * @returns Promise that resolves when all images are loaded
 */
export function preloadImages(srcs: string[]): Promise<HTMLImageElement[]> {
  return Promise.all(srcs.map(preloadImage));
}

/**
 * Preload a script
 * @param src Script URL to preload
 * @param options Additional script options
 * @returns Promise that resolves when the script is loaded
 */
export function preloadScript(
  src: string,
  options: { async?: boolean; defer?: boolean } = {}
): Promise<HTMLScriptElement> {
  return new Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.src = src;

    if (options.async) script.async = true;
    if (options.defer) script.defer = true;

    script.onload = () => resolve(script);
    script.onerror = reject;

    document.head.appendChild(script);
  });
}

/**
 * Preload a stylesheet
 * @param href Stylesheet URL to preload
 * @returns Promise that resolves when the stylesheet is loaded
 */
export function preloadStylesheet(href: string): Promise<HTMLLinkElement> {
  return new Promise((resolve, reject) => {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = href;

    link.onload = () => resolve(link);
    link.onerror = reject;

    document.head.appendChild(link);
  });
}

/**
 * Preload a font
 * @param href Font URL to preload
 * @param fontFormat Font format (e.g., 'woff2', 'woff', 'truetype')
 * @returns The created link element or null if there was an error
 */
export function preloadFont(
  href: string,
  fontFormat: 'woff2' | 'woff' | 'truetype' = 'woff2'
): HTMLLinkElement | null {
  try {
    // Check if document.head exists
    if (!document.head) {
      console.error('Cannot preload font: document.head is not available');
      return null;
    }

    // Check if this font is already preloaded
    const existingLink = document.querySelector(`link[rel="preload"][href="${href}"]`);
    if (existingLink) {
      return existingLink as HTMLLinkElement;
    }

    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = href;
    link.as = 'font';
    link.type = `font/${fontFormat}`;
    link.crossOrigin = 'anonymous';

    document.head.appendChild(link);
    return link;
  } catch (error) {
    console.error('Error preloading font:', error);
    return null;
  }
}

/**
 * Add a preconnect hint for a domain
 * @param domain Domain to preconnect to
 * @param crossOrigin Whether to include crossorigin attribute
 * @returns The created link element or null if there was an error
 */
export function preconnect(
  domain: string,
  crossOrigin = true
): HTMLLinkElement | null {
  try {
    // Check if document.head exists
    if (!document.head) {
      console.error('Cannot add preconnect: document.head is not available');
      return null;
    }

    // Check if this domain is already preconnected
    const existingLink = document.querySelector(`link[rel="preconnect"][href="${domain}"]`);
    if (existingLink) {
      return existingLink as HTMLLinkElement;
    }

    const link = document.createElement('link');
    link.rel = 'preconnect';
    link.href = domain;

    if (crossOrigin) {
      link.crossOrigin = 'anonymous';
    }

    document.head.appendChild(link);
    return link;
  } catch (error) {
    console.error('Error adding preconnect:', error);
    return null;
  }
}

/**
 * Add a DNS prefetch hint for a domain
 * @param domain Domain to prefetch DNS for
 * @returns The created link element or null if there was an error
 */
export function dnsPrefetch(domain: string): HTMLLinkElement | null {
  try {
    // Check if document.head exists
    if (!document.head) {
      console.error('Cannot add DNS prefetch: document.head is not available');
      return null;
    }

    // Check if this domain is already prefetched
    const existingLink = document.querySelector(`link[rel="dns-prefetch"][href="${domain}"]`);
    if (existingLink) {
      return existingLink as HTMLLinkElement;
    }

    const link = document.createElement('link');
    link.rel = 'dns-prefetch';
    link.href = domain;

    document.head.appendChild(link);
    return link;
  } catch (error) {
    console.error('Error adding DNS prefetch:', error);
    return null;
  }
}

/**
 * Preload critical resources for the application
 */
export function preloadCriticalResources(): void {
  try {
    // Preconnect to domains
    preconnect('https://fonts.googleapis.com');
    preconnect('https://fonts.gstatic.com');

    // Preload critical images
    preloadImages([
      '/images/logo.png',
      '/images/hero-background.jpg',
    ]);

    // Preload product images
    preloadProductImages();

    // Preload critical fonts
    preloadFont('/fonts/playfair-display.woff2');
    preloadFont('/fonts/montserrat.woff2');
  } catch (error) {
    console.error('Error preloading critical resources:', error);
  }
}

/**
 * Preload product images
 */
export function preloadProductImages(): void {
  try {
    // Import the products data
    import('@/data/products').then(({ products }) => {
      // Get all product image paths
      const imagePaths = products.map(product => product.image);

      // Preload the images
      console.log('Preloading product images:', imagePaths);
      preloadImages(imagePaths);
    }).catch(error => {
      console.error('Error importing products data:', error);
    });
  } catch (error) {
    console.error('Error preloading product images:', error);
  }
}
