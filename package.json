{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "server": "node --experimental-modules server/index.js", "dev:server": "nodemon --experimental-modules server/index.js", "start": "npm run build && npm run server", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:contexts": "jest src/contexts", "test:components": "jest src/components", "test:utils": "jest src/utils", "test:performance": "jest --testNamePattern='performance|Performance'", "test:accessibility": "jest --testNamePattern='a11y|accessibility|Accessibility'", "test:integration": "jest --testNamePattern='integration|Integration'", "test:unit": "jest --testNamePattern='unit|Unit' --testPathIgnorePatterns='integration'", "test:checkout": "node --experimental-modules server/test/run-checkout-tests.js", "test:server": "node --experimental-modules server/test/checkout-test-server.js", "test:e2e": "playwright test", "test:ci": "jest --coverage --watchAll=false --passWithNoTests", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "security:audit": "npm audit --audit-level moderate", "prepare": "husky install || true"}, "dependencies": {"@auth0/auth0-react": "^2.3.0", "@hookform/resolvers": "^3.9.0", "@paypal/react-paypal-js": "^8.8.3", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@sendgrid/mail": "^8.1.5", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.0", "@tanstack/react-query": "^5.56.2", "@types/dompurify": "^3.0.5", "@types/react-helmet": "^6.1.11", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "dompurify": "^3.2.6", "embla-carousel-react": "^8.3.0", "input-otp": "^1.2.4", "isomorphic-dompurify": "^2.25.0", "lucide-react": "^0.511.0", "micro": "^10.0.1", "next-intl": "^4.1.0", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.53.0", "react-intersection-observer": "^9.16.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "sonner": "^1.7.4", "stripe": "^18.1.1", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.3", "zod": "^3.23.8"}, "devDependencies": {"@browsermcp/mcp": "^0.1.3", "@eslint/js": "^9.9.0", "@modelcontextprotocol/server-filesystem": "^2025.3.28", "@playwright/test": "^1.40.0", "@tailwindcss/typography": "^0.5.15", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.5", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vite-pwa/assets-generator": "^1.0.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "body-parser": "^1.20.2", "chalk": "^5.3.0", "cors": "^2.8.5", "dotenv": "^16.4.5", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "express": "^4.19.2", "express-rate-limit": "^7.1.5", "globals": "^15.9.0", "husky": "^8.0.3", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lovable-tagger": "^1.1.7", "mongodb": "^6.3.0", "nodemon": "^3.1.0", "postcss": "^8.4.47", "prettier": "^3.0.3", "puppeteer": "^22.5.0", "rollup-plugin-visualizer": "^5.14.0", "tailwindcss": "^3.4.11", "ts-jest": "^29.1.1", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "uuid": "^9.0.1", "vite": "^5.4.1", "vite-plugin-imagemin": "^0.6.1"}}