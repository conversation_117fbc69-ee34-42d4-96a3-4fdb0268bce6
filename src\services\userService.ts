import { toast } from 'sonner';

// Storage key for user profile data
const USER_PROFILES_STORAGE_KEY = 'sabone-user-profiles';
const USER_IMAGES_STORAGE_KEY = 'sabone-user-images';

// User profile interface
export interface UserProfile {
  userId: string;
  name?: string;
  email?: string;
  phone?: string;
  picture?: string;
  defaultShippingAddressId?: string;
  defaultBillingAddressId?: string;
  lastUpdated: string;
}

// Get user profile
export const getUserProfile = (userId: string): UserProfile | null => {
  try {
    const profilesJson = localStorage.getItem(USER_PROFILES_STORAGE_KEY);
    if (!profilesJson) return null;

    const profiles: UserProfile[] = JSON.parse(profilesJson);
    return profiles.find(profile => profile.userId === userId) || null;
  } catch (error) {
    console.error('Error getting user profile:', error);
    return null;
  }
};

// Save user profile
export const saveUserProfile = (profile: UserProfile): boolean => {
  try {
    const profilesJson = localStorage.getItem(USER_PROFILES_STORAGE_KEY);
    const profiles: UserProfile[] = profilesJson ? JSON.parse(profilesJson) : [];

    const existingIndex = profiles.findIndex(p => p.userId === profile.userId);
    if (existingIndex >= 0) {
      profiles[existingIndex] = {
        ...profiles[existingIndex],
        ...profile,
        lastUpdated: new Date().toISOString()
      };
    } else {
      profiles.push({
        ...profile,
        lastUpdated: new Date().toISOString()
      });
    }

    localStorage.setItem(USER_PROFILES_STORAGE_KEY, JSON.stringify(profiles));
    return true;
  } catch (error) {
    console.error('Error saving user profile:', error);
    return false;
  }
};

// Upload profile picture
export const uploadProfilePicture = async (userId: string, file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    try {
      const reader = new FileReader();

      reader.onload = (event) => {
        if (!event.target?.result) {
          reject(new Error('Failed to read file'));
          return;
        }

        // Generate a unique filename
        const filename = `profile-${userId}-${Date.now()}-${file.name.replace(/[^a-zA-Z0-9.]/g, '-')}`;
        const path = `/lovable-uploads/${filename}`;

        // Store the image data
        try {
          // Get existing images
          const imagesJson = localStorage.getItem(USER_IMAGES_STORAGE_KEY);
          const images = imagesJson ? JSON.parse(imagesJson) : {};

          // Add new image
          images[path] = event.target.result;
          localStorage.setItem(USER_IMAGES_STORAGE_KEY, JSON.stringify(images));

          // Update user profile with new picture
          const profile = getUserProfile(userId) || { 
            userId, 
            lastUpdated: new Date().toISOString() 
          };
          
          profile.picture = path;
          saveUserProfile(profile);

          resolve(path);
        } catch (e) {
          reject(new Error('Failed to store image'));
        }
      };

      reader.onerror = () => {
        reject(new Error('Failed to read file'));
      };

      reader.readAsDataURL(file);
    } catch (error) {
      reject(error);
    }
  });
};

// Set default address
export const setDefaultAddress = (
  userId: string, 
  addressId: string, 
  type: 'shipping' | 'billing'
): boolean => {
  try {
    const profile = getUserProfile(userId) || { 
      userId, 
      lastUpdated: new Date().toISOString() 
    };
    
    if (type === 'shipping') {
      profile.defaultShippingAddressId = addressId;
    } else {
      profile.defaultBillingAddressId = addressId;
    }
    
    return saveUserProfile(profile);
  } catch (error) {
    console.error(`Error setting default ${type} address:`, error);
    return false;
  }
};
