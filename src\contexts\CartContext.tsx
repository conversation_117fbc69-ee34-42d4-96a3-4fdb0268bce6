import React, { createContext, useContext, useState, useEffect, useMemo, useCallback } from 'react';
import { Product } from '@/data/products';
import { toast } from 'sonner';
import { getProductInventory } from '@/services/inventoryService';

export interface CartItem {
  product: Product;
  quantity: number;
}

interface CartContextType {
  items: CartItem[];
  addItem: (product: Product, quantity?: number) => void;
  removeItem: (productId: string) => void;
  updateQuantity: (productId: string, quantity: number) => void;
  clearCart: () => void;
  itemCount: number;
  subtotal: number;
  shipping: number;
  total: number;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export const CartProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Initialize cart from localStorage if available
  const [items, setItems] = useState<CartItem[]>(() => {
    if (typeof window === 'undefined') return [];

    try {
      const savedCart = localStorage.getItem('sabone-cart');
      return savedCart ? JSON.parse(savedCart) : [];
    } catch (error) {
      console.error('Error loading cart from localStorage:', error);
      return [];
    }
  });

  const shipping = 5.99; // Fixed shipping cost for now

  // Save cart to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('sabone-cart', JSON.stringify(items));
    }
  }, [items]);

  // Memoized calculations for better performance
  const itemCount = useMemo(() => {
    return items.reduce((total, item) => total + item.quantity, 0);
  }, [items]);

  const subtotal = useMemo(() => {
    return items.reduce((total, item) => total + item.product.price * item.quantity, 0);
  }, [items]);

  const total = useMemo(() => subtotal + shipping, [subtotal, shipping]);

  const addItem = useCallback((product: Product, quantity = 1) => {
    // Check inventory before adding to cart
    const inventoryItem = getProductInventory(product.id);

    if (!inventoryItem || !inventoryItem.isInStock) {
      toast.error(`${product.name} is out of stock`);
      return;
    }

    // Limit quantity to available stock
    const availableQuantity = inventoryItem.stockQuantity;
    const qtyToAdd = Math.min(quantity, availableQuantity);

    if (qtyToAdd < quantity) {
      toast.warning(`Only ${availableQuantity} units of ${product.name} available`);
    }

    setItems((prevItems) => {
      // Check if the product is already in the cart
      const existingItemIndex = prevItems.findIndex(
        (item) => item.product.id === product.id
      );

      if (existingItemIndex !== -1) {
        // If the product is already in the cart, update the quantity
        const updatedItems = [...prevItems];
        const newQuantity = updatedItems[existingItemIndex].quantity + qtyToAdd;

        // Check if the new quantity exceeds available stock
        if (newQuantity > availableQuantity) {
          updatedItems[existingItemIndex].quantity = availableQuantity;
          toast.warning(`Cart updated to maximum available quantity (${availableQuantity})`);
        } else {
          updatedItems[existingItemIndex].quantity = newQuantity;
          toast.success(`Updated ${product.name} quantity in cart`);
        }

        return updatedItems;
      } else {
        // If the product is not in the cart, add it
        toast.success(`${product.name} added to cart`);
        return [...prevItems, { product, quantity: qtyToAdd }];
      }
    });
  }, []);

  const removeItem = useCallback((productId: string) => {
    setItems((prevItems) => {
      const itemToRemove = prevItems.find(item => item.product.id === productId);
      if (itemToRemove) {
        toast.success(`${itemToRemove.product.name} removed from cart`);
      }
      return prevItems.filter((item) => item.product.id !== productId);
    });
  }, []);

  const updateQuantity = useCallback((productId: string, quantity: number) => {
    if (quantity < 1) return;

    // Check inventory before updating quantity
    const inventoryItem = getProductInventory(productId);

    if (!inventoryItem || !inventoryItem.isInStock) {
      toast.error(`This product is out of stock`);
      return;
    }

    // Limit quantity to available stock
    const availableQuantity = inventoryItem.stockQuantity;
    const qtyToUpdate = Math.min(quantity, availableQuantity);

    if (qtyToUpdate < quantity) {
      toast.warning(`Only ${availableQuantity} units available`);
    }

    setItems((prevItems) => {
      const itemToUpdate = prevItems.find(item => item.product.id === productId);

      return prevItems.map((item) =>
        item.product.id === productId ? { ...item, quantity: qtyToUpdate } : item
      );
    });
  }, []);

  const clearCart = useCallback(() => {
    setItems([]);
    if (typeof window !== 'undefined') {
      localStorage.removeItem('sabone-cart');
    }
    toast.success('Cart cleared');
  }, []);

  return (
    <CartContext.Provider
      value={{
        items,
        addItem,
        removeItem,
        updateQuantity,
        clearCart,
        itemCount,
        subtotal,
        shipping,
        total
      }}
    >
      {children}
    </CartContext.Provider>
  );
};

export const useCart = () => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};
