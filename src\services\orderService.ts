import { CartItem } from "@/contexts/CartContext";
import {
  Order,
  OrderItem,
  Address,
  OrderStatus,
  PaymentStatus,
  PaymentMethod,
  generateOrderId,
  cartItemsToOrderItems
} from "@/types/order";

// In a real application, this would be an API call to a backend service
// For now, we'll use localStorage to simulate order persistence

const ORDERS_STORAGE_KEY = 'sabone-orders';

// Get all orders for a user
export const getUserOrders = (userId: string): Order[] => {
  try {
    const ordersJson = localStorage.getItem(ORDERS_STORAGE_KEY);
    const orders: Order[] = ordersJson ? JSON.parse(ordersJson) : [];
    return orders.filter(order => order.userId === userId);
  } catch (error) {
    console.error('Error getting user orders:', error);
    return [];
  }
};

// Get a specific order by ID
export const getOrderById = (orderId: string): Order | null => {
  try {
    const ordersJson = localStorage.getItem(ORDERS_STORAGE_KEY);
    const orders: Order[] = ordersJson ? JSON.parse(ordersJson) : [];
    return orders.find(order => order.id === orderId) || null;
  } catch (error) {
    console.error('Error getting order by ID:', error);
    return null;
  }
};

// Create a new order
export const createOrder = (
  userId: string,
  cartItems: CartItem[],
  shippingAddress: Address,
  billingAddress: Address | undefined,
  paymentMethod: PaymentMethod,
  paymentId?: string // Add paymentId parameter
): Order => {
  try {
    const orderItems = cartItemsToOrderItems(cartItems);
    const subtotal = orderItems.reduce((total, item) => total + (item.price * item.quantity), 0);
    const shipping = 5.99; // Fixed shipping cost for now
    const tax = subtotal * 0.07; // 7% tax rate for example

    const newOrder: Order = {
      id: generateOrderId(),
      userId,
      items: orderItems,
      shippingAddress,
      billingAddress,
      subtotal,
      shipping,
      tax,
      total: subtotal + shipping + tax,
      status: 'pending',
      paymentStatus: paymentMethod === 'cash_on_delivery' ? 'pending' : 'paid',
      paymentMethod,
      paymentId, // Include the payment ID from Stripe
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    // Save the order to localStorage
    const ordersJson = localStorage.getItem(ORDERS_STORAGE_KEY);
    const orders: Order[] = ordersJson ? JSON.parse(ordersJson) : [];
    orders.push(newOrder);
    localStorage.setItem(ORDERS_STORAGE_KEY, JSON.stringify(orders));

    return newOrder;
  } catch (error) {
    console.error('Error creating order:', error);
    throw new Error('Failed to create order');
  }
};

// Update order status
export const updateOrderStatus = (
  orderId: string,
  status: OrderStatus,
  paymentStatus?: PaymentStatus
): Order | null => {
  try {
    const ordersJson = localStorage.getItem(ORDERS_STORAGE_KEY);
    const orders: Order[] = ordersJson ? JSON.parse(ordersJson) : [];

    const orderIndex = orders.findIndex(order => order.id === orderId);
    if (orderIndex === -1) return null;

    const updatedOrder = {
      ...orders[orderIndex],
      status,
      paymentStatus: paymentStatus || orders[orderIndex].paymentStatus,
      updatedAt: new Date().toISOString()
    };

    orders[orderIndex] = updatedOrder;
    localStorage.setItem(ORDERS_STORAGE_KEY, JSON.stringify(orders));

    return updatedOrder;
  } catch (error) {
    console.error('Error updating order status:', error);
    return null;
  }
};

// Cancel an order
export const cancelOrder = (orderId: string): Order | null => {
  return updateOrderStatus(orderId, 'cancelled');
};
