import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useAuth } from "@/contexts/AuthContext";
import { useCart } from "@/contexts/CartContext";
import { useOrder } from "@/contexts/OrderContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { toast } from "sonner";
import StripeProvider from "./StripeProvider";
import StripePayment from "./StripePayment";
import PayPalProvider from "./PayPalProvider";
import PayPalPayment from "./PayPalPayment";
import { useIsMobile } from "@/hooks/use-mobile";
import { ChevronRight, ChevronLeft, CreditCard, Truck, User } from "lucide-react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

const formSchema = z.object({
  fullName: z.string().min(2, { message: "Full name must be at least 2 characters." }),
  email: z.string().email({ message: "Please enter a valid email address." }),
  phone: z.string().min(10, { message: "Please enter a valid phone number." }),
  addressLine1: z.string().min(5, { message: "Address must be at least 5 characters." }),
  addressLine2: z.string().optional(),
  city: z.string().min(2, { message: "City must be at least 2 characters." }),
  state: z.string().min(2, { message: "State must be at least 2 characters." }),
  zipCode: z.string().min(5, { message: "Zip code must be at least 5 characters." }),
  country: z.string().min(2, { message: "Country must be at least 2 characters." }),
  sameAsBilling: z.boolean().default(true),
  paymentMethod: z.enum(['credit_card', 'cash_on_delivery', 'paypal']),
});

type FormValues = z.infer<typeof formSchema>;

// Form steps for mobile checkout flow
type CheckoutStep = 'customer' | 'shipping' | 'payment';

const CustomerForm = () => {
  const { isAuthenticated, user } = useAuth();
  const { total } = useCart();
  const { createOrder } = useOrder();
  const isMobile = useIsMobile();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [paymentMethodId, setPaymentMethodId] = useState<string | null>(null);
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [_tempOrderId, _setTempOrderId] = useState<string>(`temp-${Math.random().toString(36).substring(2)}`);
  const [currentStep, setCurrentStep] = useState<CheckoutStep>('customer');

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fullName: user?.name || "",
      email: user?.email || "",
      phone: user?.phone_number || "",
      addressLine1: "",
      addressLine2: "",
      city: "",
      state: "",
      zipCode: "",
      country: "",
      sameAsBilling: true,
      paymentMethod: 'cash_on_delivery',
    },
    mode: "onChange", // Enable real-time validation feedback
  });

  const paymentMethod = form.watch('paymentMethod');
  const sameAsBilling = form.watch('sameAsBilling');

  const handleStripeSuccess = (paymentMethodId: string) => {
    setPaymentMethodId(paymentMethodId);
    toast.success("Credit card verified. Ready to place order.");
  };

  const handlePayPalSuccess = (transactionId: string) => {
    setPaymentMethodId(transactionId);
    toast.success("PayPal payment processed. Ready to place order.");
  };

  const handlePaymentError = (error: Error) => {
    // Log error for debugging in development
    if (process.env.NODE_ENV === 'development') {
      console.error("Payment error:", error);
    }
    toast.error("Payment verification failed. Please try again.");
  };

  // Mobile checkout step navigation
  const goToNextStep = () => {
    if (currentStep === 'customer') {
      // Validate customer information fields before proceeding
      const customerFields = ['fullName', 'email', 'phone'];
      const isValid = customerFields.every(field => form.getFieldState(field).invalid !== true);

      if (isValid) {
        setCurrentStep('shipping');
        window.scrollTo({ top: 0, behavior: 'smooth' });
      } else {
        // Trigger validation on customer fields
        customerFields.forEach(field => form.trigger(field));
        toast.error("Please complete all required customer information");
      }
    } else if (currentStep === 'shipping') {
      // Validate shipping address fields before proceeding
      const shippingFields = ['addressLine1', 'city', 'state', 'zipCode', 'country'];
      const isValid = shippingFields.every(field => form.getFieldState(field).invalid !== true);

      if (isValid) {
        setCurrentStep('payment');
        window.scrollTo({ top: 0, behavior: 'smooth' });
      } else {
        // Trigger validation on shipping fields
        shippingFields.forEach(field => form.trigger(field));
        toast.error("Please complete all required shipping information");
      }
    }
  };

  const goToPreviousStep = () => {
    if (currentStep === 'shipping') {
      setCurrentStep('customer');
    } else if (currentStep === 'payment') {
      setCurrentStep('shipping');
    }
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const onSubmit = async (data: FormValues) => {
    if (!isAuthenticated) {
      toast.error("Please log in to place an order");
      return;
    }

    if ((data.paymentMethod === 'credit_card' || data.paymentMethod === 'paypal') && !paymentMethodId) {
      toast.error("Please complete the payment information");
      return;
    }

    setIsSubmitting(true);

    try {
      // Create shipping address object
      const shippingAddress = {
        fullName: data.fullName,
        addressLine1: data.addressLine1,
        addressLine2: data.addressLine2,
        city: data.city,
        state: data.state,
        zipCode: data.zipCode,
        country: data.country,
        phone: data.phone,
      };

      // Create billing address (same as shipping or different)
      const billingAddress = data.sameAsBilling ? shippingAddress : undefined;

      // Create the order with payment ID if available
      const order = await createOrder(
        shippingAddress,
        billingAddress,
        data.paymentMethod,
        data.paymentMethod === 'credit_card' ? paymentMethodId : undefined
      );

      if (order) {
        // Reset form and redirect to order confirmation
        form.reset();
        toast.success("Order placed successfully!");

        // In a real application, redirect to order confirmation page
        setTimeout(() => {
          window.location.href = `/account/orders`;
        }, 2000);
      }
    } catch (error) {
      // Log error for debugging in development
      if (process.env.NODE_ENV === 'development') {
        console.error("Error placing order:", error);
      }
      toast.error("Failed to place order. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Progress indicator for mobile checkout
  const renderProgressIndicator = () => {
    if (!isMobile) return null;

    return (
      <div className="mb-6">
        <div className="flex justify-between items-center">
          <div className={`flex flex-col items-center ${currentStep === 'customer' ? 'text-sabone-gold' : 'text-sabone-cream/60'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center mb-1 ${currentStep === 'customer' ? 'bg-sabone-gold text-sabone-charcoal' : 'bg-sabone-dark-olive border border-sabone-gold/30'}`}>
              <User size={16} />
            </div>
            <span className="text-xs">Customer</span>
          </div>

          <div className={`w-16 h-0.5 ${currentStep === 'customer' ? 'bg-sabone-gold/30' : currentStep === 'shipping' || currentStep === 'payment' ? 'bg-sabone-gold' : 'bg-sabone-gold/30'}`} />

          <div className={`flex flex-col items-center ${currentStep === 'shipping' ? 'text-sabone-gold' : 'text-sabone-cream/60'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center mb-1 ${currentStep === 'shipping' ? 'bg-sabone-gold text-sabone-charcoal' : 'bg-sabone-dark-olive border border-sabone-gold/30'}`}>
              <Truck size={16} />
            </div>
            <span className="text-xs">Shipping</span>
          </div>

          <div className={`w-16 h-0.5 ${currentStep === 'shipping' ? 'bg-sabone-gold/30' : currentStep === 'payment' ? 'bg-sabone-gold' : 'bg-sabone-gold/30'}`} />

          <div className={`flex flex-col items-center ${currentStep === 'payment' ? 'text-sabone-gold' : 'text-sabone-cream/60'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center mb-1 ${currentStep === 'payment' ? 'bg-sabone-gold text-sabone-charcoal' : 'bg-sabone-dark-olive border border-sabone-gold/30'}`}>
              <CreditCard size={16} />
            </div>
            <span className="text-xs">Payment</span>
          </div>
        </div>
      </div>
    );
  };

  return (
    <Form {...form}>
      <form id="customer-form" onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {isMobile && renderProgressIndicator()}

        {/* Customer Information Section - Always visible on desktop, conditionally on mobile */}
        <div className={`space-y-4 ${isMobile && currentStep !== 'customer' ? 'hidden' : 'block'}`}>
          <h3 className="text-lg font-medium text-sabone-cream">Contact Information</h3>

          <div className="grid gap-4 sm:grid-cols-2">
            <FormField
              control={form.control}
              name="fullName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sabone-cream">Full Name</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream h-12 text-base"
                      placeholder="Your full name"
                      autoComplete="name"
                      // Larger touch target on mobile
                      style={{ fontSize: isMobile ? '16px' : undefined }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sabone-cream">Email</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="email"
                      className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream h-12 text-base"
                      placeholder="<EMAIL>"
                      inputMode="email"
                      autoComplete="email"
                      // Prevent zoom on mobile by using larger font
                      style={{ fontSize: isMobile ? '16px' : undefined }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sabone-cream">Phone</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream h-12 text-base"
                      placeholder="Your phone number"
                      inputMode="tel"
                      autoComplete="tel"
                      // Use tel input type on mobile
                      type={isMobile ? "tel" : "text"}
                      // Prevent zoom on mobile by using larger font
                      style={{ fontSize: isMobile ? '16px' : undefined }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {isMobile && (
            <Button
              type="button"
              onClick={goToNextStep}
              className="w-full mt-4 bg-sabone-gold hover:bg-sabone-gold/80 text-sabone-charcoal font-medium h-12"
            >
              Continue to Shipping <ChevronRight className="ml-2 h-4 w-4" />
            </Button>
          )}
        </div>

        {!isMobile && <Separator className="bg-sabone-gold/20" />}

        {/* Shipping Address Section - Always visible on desktop, conditionally on mobile */}
        <div className={`space-y-4 ${isMobile && currentStep !== 'shipping' ? 'hidden' : 'block'}`}>
          <h3 className="text-lg font-medium text-sabone-cream">Shipping Address</h3>

          <FormField
            control={form.control}
            name="addressLine1"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sabone-cream">Address Line 1</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream h-12 text-base"
                    placeholder="Street address"
                    autoComplete="address-line1"
                    style={{ fontSize: isMobile ? '16px' : undefined }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="addressLine2"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sabone-cream">Address Line 2 (Optional)</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream h-12 text-base"
                    placeholder="Apartment, suite, etc."
                    autoComplete="address-line2"
                    style={{ fontSize: isMobile ? '16px' : undefined }}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid gap-4 sm:grid-cols-2">
            <FormField
              control={form.control}
              name="city"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sabone-cream">City</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream h-12 text-base"
                      placeholder="City"
                      autoComplete="address-level2"
                      style={{ fontSize: isMobile ? '16px' : undefined }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="state"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sabone-cream">State/Province</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream h-12 text-base"
                      placeholder="State or province"
                      autoComplete="address-level1"
                      style={{ fontSize: isMobile ? '16px' : undefined }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="zipCode"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sabone-cream">ZIP/Postal Code</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream h-12 text-base"
                      placeholder="ZIP or postal code"
                      inputMode="numeric"
                      autoComplete="postal-code"
                      style={{ fontSize: isMobile ? '16px' : undefined }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="country"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sabone-cream">Country</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream h-12 text-base"
                      placeholder="Country"
                      autoComplete="country-name"
                      style={{ fontSize: isMobile ? '16px' : undefined }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {isMobile && (
            <div className="flex flex-col space-y-2 mt-4">
              <Button
                type="button"
                onClick={goToNextStep}
                className="w-full bg-sabone-gold hover:bg-sabone-gold/80 text-sabone-charcoal font-medium h-12"
              >
                Continue to Payment <ChevronRight className="ml-2 h-4 w-4" />
              </Button>
              <Button
                type="button"
                onClick={goToPreviousStep}
                variant="outline"
                className="w-full border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10 h-12"
              >
                <ChevronLeft className="mr-2 h-4 w-4" /> Back to Customer Info
              </Button>
            </div>
          )}
        </div>

        {!isMobile && <Separator className="bg-sabone-gold/20" />}

        {/* Billing Information Section - Always visible on desktop, conditionally on mobile */}
        <div className={`space-y-4 ${isMobile && currentStep !== 'payment' ? 'hidden' : 'block'}`}>
          <h3 className="text-lg font-medium text-sabone-cream">Billing Information</h3>

          <div className="flex items-center space-x-2">
            <FormField
              control={form.control}
              name="sameAsBilling"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      className="data-[state=checked]:bg-sabone-gold"
                    />
                  </FormControl>
                  <FormLabel className="text-sabone-cream">
                    Billing address same as shipping
                  </FormLabel>
                </FormItem>
              )}
            />
          </div>

          {!sameAsBilling && (
            <div className="bg-sabone-dark-olive/60 p-4 rounded-md mt-4">
              <p className="text-sabone-cream/70 text-sm">
                Separate billing address will be implemented in the future.
              </p>
            </div>
          )}
        </div>

        {!isMobile && <Separator className="bg-sabone-gold/20" />}

        {/* Payment Method Section - Always visible on desktop, conditionally on mobile */}
        <div className={`space-y-4 ${isMobile && currentStep !== 'payment' ? 'hidden' : 'block'}`}>
          <h3 className="text-lg font-medium text-sabone-cream">Payment Method</h3>

          <FormField
            control={form.control}
            name="paymentMethod"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    className="flex flex-col space-y-2"
                  >
                    {/* Enhanced payment method options for better mobile experience */}
                    <FormItem className={`flex items-center space-x-3 space-y-0 ${isMobile ? 'bg-sabone-dark-olive/60 p-3 rounded-md' : ''}`}>
                      <FormControl>
                        <RadioGroupItem
                          value="cash_on_delivery"
                          className="text-sabone-gold border-sabone-gold/50 data-[state=checked]:bg-sabone-gold data-[state=checked]:text-sabone-charcoal h-5 w-5"
                        />
                      </FormControl>
                      <FormLabel className="text-sabone-cream text-base flex-grow">
                        Cash on Delivery
                      </FormLabel>
                    </FormItem>
                    <FormItem className={`flex items-center space-x-3 space-y-0 ${isMobile ? 'bg-sabone-dark-olive/60 p-3 rounded-md' : ''}`}>
                      <FormControl>
                        <RadioGroupItem
                          value="credit_card"
                          className="text-sabone-gold border-sabone-gold/50 data-[state=checked]:bg-sabone-gold data-[state=checked]:text-sabone-charcoal h-5 w-5"
                        />
                      </FormControl>
                      <FormLabel className="text-sabone-cream text-base flex-grow">
                        Credit Card
                      </FormLabel>
                      {isMobile && (
                        <div className="flex space-x-1">
                          <div className="w-8 h-5 bg-gray-700 rounded-sm flex items-center justify-center">
                            <span className="text-xs text-white">Visa</span>
                          </div>
                          <div className="w-8 h-5 bg-gray-700 rounded-sm flex items-center justify-center">
                            <span className="text-xs text-white">MC</span>
                          </div>
                        </div>
                      )}
                    </FormItem>
                    <FormItem className={`flex items-center space-x-3 space-y-0 ${isMobile ? 'bg-sabone-dark-olive/60 p-3 rounded-md' : ''}`}>
                      <FormControl>
                        <RadioGroupItem
                          value="paypal"
                          className="text-sabone-gold border-sabone-gold/50 data-[state=checked]:bg-sabone-gold data-[state=checked]:text-sabone-charcoal h-5 w-5"
                        />
                      </FormControl>
                      <FormLabel className="text-sabone-cream text-base flex-grow">
                        PayPal
                      </FormLabel>
                      {isMobile && (
                        <div className="w-12 h-5 bg-[#003087] rounded-sm flex items-center justify-center">
                          <span className="text-xs text-white font-bold">PayPal</span>
                        </div>
                      )}
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {paymentMethod === 'credit_card' && (
            <div className="mt-4 p-4 bg-sabone-dark-olive/60 rounded-md">
              <h4 className="text-sabone-gold font-medium mb-4">Credit Card Information</h4>
              <StripeProvider clientSecret={clientSecret || undefined}>
                <StripePayment
                  amount={total}
                  onSuccess={handleStripeSuccess}
                  onError={handlePaymentError}
                  disabled={isSubmitting}
                  clientSecret={clientSecret || undefined}
                  setClientSecret={setClientSecret}
                />
              </StripeProvider>
            </div>
          )}

          {paymentMethod === 'paypal' && (
            <div className="mt-4 p-4 bg-sabone-dark-olive/60 rounded-md">
              <h4 className="text-sabone-gold font-medium mb-4">PayPal Payment</h4>
              <PayPalProvider>
                <PayPalPayment
                  amount={total}
                  orderId={tempOrderId}
                  onSuccess={handlePayPalSuccess}
                  onError={handlePaymentError}
                  disabled={isSubmitting}
                />
              </PayPalProvider>
            </div>
          )}

          {isMobile && (
            <Button
              type="button"
              onClick={goToPreviousStep}
              variant="outline"
              className="w-full mt-4 border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10 h-12"
            >
              <ChevronLeft className="mr-2 h-4 w-4" /> Back to Shipping
            </Button>
          )}
        </div>

        <div className="pt-6">
          <Button
            type="submit"
            className="w-full bg-sabone-gold hover:bg-sabone-gold/80 text-sabone-charcoal font-medium h-14 text-base"
            disabled={isSubmitting || !isAuthenticated}
          >
            {isSubmitting ? 'Processing...' : 'Place Order'}
          </Button>

          {!isAuthenticated && (
            <p className="text-red-400 text-sm mt-2 text-center">
              Please log in to place an order
            </p>
          )}
        </div>
      </form>
    </Form>
  );
};

export default CustomerForm;
