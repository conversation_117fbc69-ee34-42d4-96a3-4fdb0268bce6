import { renderHook, act } from '@testing-library/react';
import { toast } from 'sonner';
import { useTokenManagement } from '../useTokenManagement';

// Mock sonner toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

describe('useTokenManagement', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('should initialize with default state', () => {
    const { result } = renderHook(() => useTokenManagement());
    
    expect(result.current.tokenRefreshAttempts).toBe(0);
    expect(result.current.lastTokenRefresh).toBe(0);
  });

  it('should reset token state', () => {
    const { result } = renderHook(() => useTokenManagement());
    
    act(() => {
      result.current.resetTokenState();
    });

    expect(result.current.tokenRefreshAttempts).toBe(0);
    expect(result.current.lastTokenRefresh).toBe(0);
  });

  describe('refreshAuth', () => {
    it('should successfully refresh authentication', async () => {
      const { result } = renderHook(() => useTokenManagement());
      const mockGetAccessToken = jest.fn().mockResolvedValue('access-token');
      const mockLogout = jest.fn();

      await act(async () => {
        await result.current.refreshAuth(mockGetAccessToken, mockLogout);
      });

      expect(mockGetAccessToken).toHaveBeenCalled();
      expect(result.current.tokenRefreshAttempts).toBe(0);
      expect(result.current.lastTokenRefresh).toBeGreaterThan(0);
      expect(toast.success).toHaveBeenCalledWith('Authentication refreshed successfully');
      expect(mockLogout).not.toHaveBeenCalled();
    });

    it('should handle refresh failure and retry', async () => {
      const { result } = renderHook(() => useTokenManagement());
      const mockGetAccessToken = jest.fn().mockRejectedValue(new Error('Refresh failed'));
      const mockLogout = jest.fn();

      await act(async () => {
        try {
          await result.current.refreshAuth(mockGetAccessToken, mockLogout);
        } catch (error) {
          // Expected to throw
        }
      });

      expect(result.current.tokenRefreshAttempts).toBe(1);
      expect(toast.error).toHaveBeenCalledWith('Failed to refresh authentication. Retrying...');
      expect(mockLogout).not.toHaveBeenCalled();
    });

    it('should logout after maximum refresh attempts', async () => {
      const { result } = renderHook(() => useTokenManagement());
      const mockGetAccessToken = jest.fn().mockRejectedValue(new Error('Refresh failed'));
      const mockLogout = jest.fn();

      // Simulate 3 failed attempts (reaching the max)
      for (let i = 0; i < 3; i++) {
        await act(async () => {
          try {
            await result.current.refreshAuth(mockGetAccessToken, mockLogout);
          } catch (error) {
            // Expected to throw
          }
        });
      }

      expect(result.current.tokenRefreshAttempts).toBe(3);
      expect(toast.error).toHaveBeenCalledWith('Authentication refresh failed. Please log in again.');
      expect(mockLogout).toHaveBeenCalled();
    });
  });

  describe('getAccessToken', () => {
    it('should return null when not authenticated', async () => {
      const { result } = renderHook(() => useTokenManagement());
      const mockGetAccessToken = jest.fn();
      const mockLogout = jest.fn();

      const token = await act(async () => {
        return await result.current.getAccessToken(false, mockGetAccessToken, false, mockLogout);
      });

      expect(token).toBeNull();
      expect(mockGetAccessToken).not.toHaveBeenCalled();
    });

    it('should return development token in development mode', async () => {
      const { result } = renderHook(() => useTokenManagement());
      const mockGetAccessToken = jest.fn();
      const mockLogout = jest.fn();

      const token = await act(async () => {
        return await result.current.getAccessToken(true, mockGetAccessToken, true, mockLogout);
      });

      expect(token).toBe('dev-token-123456');
      expect(mockGetAccessToken).not.toHaveBeenCalled();
    });

    it('should successfully get access token in production', async () => {
      const { result } = renderHook(() => useTokenManagement());
      const mockGetAccessToken = jest.fn().mockResolvedValue('access-token-123');
      const mockLogout = jest.fn();

      const token = await act(async () => {
        return await result.current.getAccessToken(true, mockGetAccessToken, false, mockLogout);
      });

      expect(token).toBe('access-token-123');
      expect(mockGetAccessToken).toHaveBeenCalled();
    });

    it('should handle login required error', async () => {
      const { result } = renderHook(() => useTokenManagement());
      const mockGetAccessToken = jest.fn().mockRejectedValue(new Error('Login required'));
      const mockLogout = jest.fn();

      const token = await act(async () => {
        return await result.current.getAccessToken(true, mockGetAccessToken, false, mockLogout);
      });

      expect(token).toBeNull();
      expect(toast.error).toHaveBeenCalledWith('Session expired. Please log in again.');
      expect(mockLogout).toHaveBeenCalled();
    });

    it('should handle login_required error', async () => {
      const { result } = renderHook(() => useTokenManagement());
      const mockGetAccessToken = jest.fn().mockRejectedValue(new Error('login_required'));
      const mockLogout = jest.fn();

      const token = await act(async () => {
        return await result.current.getAccessToken(true, mockGetAccessToken, false, mockLogout);
      });

      expect(token).toBeNull();
      expect(toast.error).toHaveBeenCalledWith('Session expired. Please log in again.');
      expect(mockLogout).toHaveBeenCalled();
    });

    it('should handle generic token errors', async () => {
      const { result } = renderHook(() => useTokenManagement());
      const mockGetAccessToken = jest.fn().mockRejectedValue(new Error('Generic token error'));
      const mockLogout = jest.fn();

      const token = await act(async () => {
        return await result.current.getAccessToken(true, mockGetAccessToken, false, mockLogout);
      });

      expect(token).toBeNull();
      expect(toast.error).toHaveBeenCalledWith('Failed to get access token. Please try refreshing the page.');
      expect(mockLogout).not.toHaveBeenCalled();
    });

    it('should reset refresh attempts on successful token retrieval after failures', async () => {
      const { result } = renderHook(() => useTokenManagement());
      const mockGetAccessToken = jest.fn();
      const mockLogout = jest.fn();

      // First, simulate a failed refresh attempt
      mockGetAccessToken.mockRejectedValueOnce(new Error('Refresh failed'));
      await act(async () => {
        try {
          await result.current.refreshAuth(mockGetAccessToken, mockLogout);
        } catch (error) {
          // Expected to throw
        }
      });

      expect(result.current.tokenRefreshAttempts).toBe(1);

      // Then simulate successful token retrieval
      mockGetAccessToken.mockResolvedValueOnce('access-token-123');
      await act(async () => {
        await result.current.getAccessToken(true, mockGetAccessToken, false, mockLogout);
      });

      expect(result.current.tokenRefreshAttempts).toBe(0);
    });
  });

  describe('token refresh cooldown', () => {
    it('should respect cooldown period between refresh attempts', async () => {
      const { result } = renderHook(() => useTokenManagement());
      const mockGetAccessToken = jest.fn().mockResolvedValue('access-token');
      const mockLogout = jest.fn();

      // First successful refresh
      await act(async () => {
        await result.current.refreshAuth(mockGetAccessToken, mockLogout);
      });

      const firstRefreshTime = result.current.lastTokenRefresh;
      expect(firstRefreshTime).toBeGreaterThan(0);

      // Advance time to ensure different timestamp
      jest.advanceTimersByTime(100);

      // Reset the mock to track new calls
      mockGetAccessToken.mockClear();

      // Attempt another refresh after advancing time
      await act(async () => {
        await result.current.refreshAuth(mockGetAccessToken, mockLogout);
      });

      expect(mockGetAccessToken).toHaveBeenCalled();
      expect(result.current.lastTokenRefresh).toBeGreaterThan(firstRefreshTime);
    });
  });
}); 