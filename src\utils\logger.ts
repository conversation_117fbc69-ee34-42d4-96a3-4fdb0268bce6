/**
 * Logger utility for structured logging across the application
 */

export type LogLevel = 'error' | 'warn' | 'info' | 'debug';

export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: string;
  context?: string;
  data?: any;
  error?: Error;
  userId?: string;
  sessionId?: string;
}

class Logger {
  private context: string;
  private isProduction: boolean;

  constructor(context: string = 'App') {
    this.context = context;
    this.isProduction = (typeof process !== 'undefined' && process.env?.NODE_ENV === 'production') || false;
  }

  /**
   * Creates a log entry with common fields
   */
  private createLogEntry(level: LogLevel, message: string, data?: any, error?: Error): LogEntry {
    return {
      level,
      message,
      timestamp: new Date().toISOString(),
      context: this.context,
      data,
      error,
      // In a real app, you would get these from user session
      userId: this.getUserId(),
      sessionId: this.getSessionId(),
    };
  }

  /**
   * Gets user ID from session/auth context
   */
  private getUserId(): string | undefined {
    // TODO: Implement actual user ID retrieval from auth context
    try {
      const userData = localStorage.getItem('auth-user');
      if (userData) {
        const user = JSON.parse(userData);
        return user.id || user.sub;
      }
    } catch (error) {
      // Ignore errors when getting user ID
    }
    return undefined;
  }

  /**
   * Gets or creates a session ID
   */
  private getSessionId(): string | undefined {
    try {
      let sessionId = sessionStorage.getItem('session-id');
      if (!sessionId) {
        sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2)}`;
        sessionStorage.setItem('session-id', sessionId);
      }
      return sessionId;
    } catch (error) {
      return undefined;
    }
  }

  /**
   * Sends log to external service in production
   */
  private async sendToExternalService(logEntry: LogEntry): Promise<void> {
    if (!this.isProduction) {
      return;
    }

    try {
      // TODO: Implement actual external logging service integration
      // Example: Sentry, LogRocket, DataDog, etc.

      // For now, we'll just prepare the structure
      const payload = {
        ...logEntry,
        environment: (typeof process !== 'undefined' && process.env?.NODE_ENV) || 'development',
        userAgent: (typeof navigator !== 'undefined' && navigator.userAgent) || 'unknown',
        url: (typeof window !== 'undefined' && window.location?.href) || 'unknown',
        referrer: (typeof document !== 'undefined' && document.referrer) || 'unknown',
      };

      // In production, you would send this to your logging service
      console.log('Would send to external service:', payload);

      // Example implementation for a hypothetical service:
      // await fetch('/api/logs', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(payload),
      // });
    } catch (error) {
      // Fail silently to avoid breaking the app
      console.error('Failed to send log to external service:', error);
    }
  }

  /**
   * Log an error message
   */
  error(message: string, error?: Error, data?: any): void {
    const logEntry = this.createLogEntry('error', message, data, error);

    // Always log errors to console
    console.error(`[${this.context}] ${message}`, { error, data });

    // Send to external service
    this.sendToExternalService(logEntry);
  }

  /**
   * Log a warning message
   */
  warn(message: string, data?: any): void {
    const logEntry = this.createLogEntry('warn', message, data);

    // Log warnings to console in development
    if (!this.isProduction) {
      console.warn(`[${this.context}] ${message}`, data);
    }

    // Send to external service
    this.sendToExternalService(logEntry);
  }

  /**
   * Log an info message
   */
  info(message: string, data?: any): void {
    const logEntry = this.createLogEntry('info', message, data);

    // Log info to console in development
    if (!this.isProduction) {
      console.info(`[${this.context}] ${message}`, data);
    }

    // Send to external service
    this.sendToExternalService(logEntry);
  }

  /**
   * Log a debug message
   */
  debug(message: string, data?: any): void {
    // Only log debug messages in development
    if (!this.isProduction) {
      console.debug(`[${this.context}] ${message}`, data);
    }

    // Don't send debug logs to external service by default
  }

  /**
   * Log a performance metric
   */
  performance(name: string, duration: number, data?: any): void {
    const message = `Performance: ${name} took ${duration}ms`;
    const logEntry = this.createLogEntry('info', message, {
      ...data,
      performance: { name, duration }
    });

    if (!this.isProduction) {
      console.info(`[${this.context}] ${message}`, data);
    }

    this.sendToExternalService(logEntry);
  }

  /**
   * Log a user action for analytics
   */
  userAction(action: string, data?: any): void {
    const message = `User action: ${action}`;
    const logEntry = this.createLogEntry('info', message, {
      ...data,
      userAction: action
    });

    if (!this.isProduction) {
      console.info(`[${this.context}] ${message}`, data);
    }

    this.sendToExternalService(logEntry);
  }

  /**
   * Creates a new logger instance with a different context
   */
  withContext(context: string): Logger {
    return new Logger(context);
  }
}

// Export singleton instances for common use cases
export const logger = new Logger('App');
export const apiLogger = new Logger('API');
export const uiLogger = new Logger('UI');
export const paymentLogger = new Logger('Payment');
export const authLogger = new Logger('Auth');

// Export the Logger class for custom instances
export { Logger };

/**
 * Performance timing utility
 */
export class PerformanceTimer {
  private startTime: number;
  private name: string;
  private logger: Logger;

  constructor(name: string, loggerInstance?: Logger) {
    this.name = name;
    this.logger = loggerInstance || logger;
    this.startTime = performance.now();
  }

  /**
   * Ends the timer and logs the duration
   */
  end(data?: any): number {
    const duration = performance.now() - this.startTime;
    this.logger.performance(this.name, duration, data);
    return duration;
  }
}

/**
 * Helper function to create a performance timer
 */
export const startTimer = (name: string, logger?: Logger): PerformanceTimer => {
  return new PerformanceTimer(name, logger);
};