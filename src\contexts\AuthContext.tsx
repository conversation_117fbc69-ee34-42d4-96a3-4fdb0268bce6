import React, { createContext, useContext, useEffect, useState, useCallback, useRef } from 'react';
import { useAuth0 } from '@auth0/auth0-react';
import { toast } from 'sonner';
import { useAuthError } from '@/hooks/useAuthError';
import { useTokenManagement } from '@/hooks/useTokenManagement';
import { useDevelopmentAuth } from '@/hooks/useDevelopmentAuth';
import { useUserProfile } from '@/hooks/useUserProfile';

interface User {
  sub?: string; // Made sub optional to match Auth0's User type
  name?: string;
  email?: string; // Made email optional to match Auth0's User type
  picture?: string;
  phone_number?: string;
  role?: 'user' | 'admin' | 'affiliate';
}

interface AuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;
  login: () => void;
  register: (name?: string, email?: string, password?: string) => void;
  logout: (callback?: () => void) => void;
  getAccessToken: () => Promise<string | null>;
  setAsAdmin: () => void; // Function to set user as admin in development mode
  updateProfile: (data: { name?: string; phone_number?: string; }) => Promise<boolean>;
  updateProfilePicture: (file: File) => Promise<string | null>;
  // New methods for better auth handling
  refreshAuth: () => Promise<void>;
  retryLogin: () => void;
  authError: Error | null;
  clearAuthError: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const {
    isAuthenticated: auth0IsAuthenticated,
    isLoading: auth0IsLoading,
    user: auth0User,
    loginWithRedirect,
    logout: auth0Logout,
    getAccessTokenSilently,
    error: auth0Error,
  } = useAuth0();

  // Use modular hooks for different auth concerns
  const { authError, setAuthError, clearAuthError, handleAuth0Error } = useAuthError();
  const { tokenRefreshAttempts, refreshAuth, getAccessToken } = useTokenManagement();
  const { 
    devUser, 
    isDevelopmentMode, 
    devLogin, 
    devRegister, 
    devLogout, 
    setAsAdmin: devSetAsAdmin,
    updateDevUser 
  } = useDevelopmentAuth();
  const { updateProfile: profileUpdate, updateProfilePicture: pictureUpdate } = useUserProfile();

  // Track authentication state changes
  const prevAuthState = useRef<boolean>(false);
  const [authStateStable, setAuthStateStable] = useState(false);

  // Enhanced error handling using modular hook
  useEffect(() => {
    handleAuth0Error(auth0Error);
  }, [auth0Error, handleAuth0Error]);

  // Track authentication state stability
  useEffect(() => {
    if (isDevelopmentMode) {
      setAuthStateStable(true);
      return;
    }

    const currentAuthState = auth0IsAuthenticated;
    if (prevAuthState.current !== currentAuthState) {
      setAuthStateStable(false);
      prevAuthState.current = currentAuthState;
      
      // Wait for state to stabilize
      const timer = setTimeout(() => {
        setAuthStateStable(true);
      }, 1000);
      
      return () => clearTimeout(timer);
    } else {
      setAuthStateStable(true);
    }
  }, [auth0IsAuthenticated, isDevelopmentMode]);

  // Development auth is now handled by useDevelopmentAuth hook

  // In development mode with VITE_SKIP_AUTH=true, we'll use a mock user
  const isAuthenticated = isDevelopmentMode ? !!devUser : auth0IsAuthenticated;
  const isLoading = isDevelopmentMode ? false : (auth0IsLoading || !authStateStable);
  const user = isDevelopmentMode ? devUser : auth0User;

  // Enhanced login success notification with error handling
  useEffect(() => {
    if (authStateStable && isAuthenticated && user && !authError) {
      const userName = user.name || user.email || 'User';
      toast.success(`Welcome, ${userName}!`);
    }
  }, [authStateStable, isAuthenticated, user, authError]);

  const login = () => {
    if (isDevelopmentMode) {
      devLogin();
      return;
    }
    
    setAuthError(null);
    loginWithRedirect({
      authorizationParams: {
        screen_hint: 'login',
      }
    }).catch((error) => {
      console.error('Login redirect failed:', error);
      setAuthError(error);
      toast.error('Failed to initiate login. Please try again.');
    });
  };

  const retryLogin = useCallback(() => {
    if (authError) {
      setAuthError(null);
      login();
    }
  }, [authError]);

  const register = (name?: string, email?: string, password?: string) => {
    if (isDevelopmentMode) {
      devRegister();
      return;
    }

    setAuthError(null);
    loginWithRedirect({
      authorizationParams: {
        screen_hint: 'signup',
      }
    }).catch((error) => {
      console.error('Registration redirect failed:', error);
      setAuthError(error);
      toast.error('Failed to initiate registration. Please try again.');
    });
  };

  const logoutUser = useCallback((callback?: () => void) => {
    if (isDevelopmentMode) {
      devLogout(callback);
      return;
    }

    setAuthError(null);
    try {
      auth0Logout({
        logoutParams: {
          returnTo: window.location.origin
        }
      });
      toast.info('You have been logged out');

      if (callback) {
        setTimeout(callback, 100);
      }
    } catch (error) {
      console.error('Logout failed:', error);
      setAuthError(error as Error);
      toast.error('Failed to log out properly. Please clear your browser cache.');
    }
  }, [auth0Logout, isDevelopmentMode, devLogout]);

  const getAccessTokenWithCallback = useCallback(async (): Promise<string | null> => {
    return getAccessToken(isAuthenticated, getAccessTokenSilently, isDevelopmentMode, logoutUser);
  }, [getAccessToken, isAuthenticated, getAccessTokenSilently, isDevelopmentMode, logoutUser]);

  // Function to set the development user as an admin
  const setAsAdmin = devSetAsAdmin;

  // Profile update methods using modular hooks
  const updateProfile = useCallback(async (data: { name?: string; phone_number?: string }): Promise<boolean> => {
    if (!isAuthenticated || !user?.sub) {
      toast.error('You must be logged in to update your profile');
      return false;
    }

    try {
      return await profileUpdate(user.sub, data, isDevelopmentMode, updateDevUser);
    } catch (error) {
      console.error('Error updating profile:', error);
      setAuthError(error as Error);
      return false;
    }
  }, [isAuthenticated, user?.sub, profileUpdate, isDevelopmentMode, updateDevUser, setAuthError]);

  const updateProfilePicture = useCallback(async (file: File): Promise<string | null> => {
    if (!isAuthenticated || !user?.sub) {
      toast.error('You must be logged in to update your profile picture');
      return null;
    }

    try {
      return await pictureUpdate(user.sub, file, isDevelopmentMode, updateDevUser);
    } catch (error) {
      console.error('Error updating profile picture:', error);
      setAuthError(error as Error);
      return null;
    }
  }, [isAuthenticated, user?.sub, pictureUpdate, isDevelopmentMode, updateDevUser, setAuthError]);

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        isLoading,
        user,
        login,
        register,
        logout: logoutUser,
        getAccessToken: getAccessTokenWithCallback,
        setAsAdmin,
        updateProfile,
        updateProfilePicture,
        refreshAuth: () => refreshAuth(getAccessTokenSilently, logoutUser),
        retryLogin: () => authError && login(),
        authError,
        clearAuthError
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
