/**
 * Frontend Security Middleware
 * 
 * This module provides comprehensive security measures for the frontend application
 * including CSP, input validation, request sanitization, and security monitoring.
 */

import { sanitizeObject, containsXssPatterns, containsSqlInjection, validateRateLimit } from '@/utils/inputSanitization';
import { toast } from 'sonner';

// Security configuration
interface SecurityConfig {
  enableCSP: boolean;
  enableRateLimit: boolean;
  enableInputValidation: boolean;
  enableSecurityHeaders: boolean;
  logSecurityEvents: boolean;
  blockSuspiciousRequests: boolean;
}

const DEFAULT_SECURITY_CONFIG: SecurityConfig = {
  enableCSP: true,
  enableRateLimit: true,
  enableInputValidation: true,
  enableSecurityHeaders: true,
  logSecurityEvents: true,
  blockSuspiciousRequests: true,
};

// Security event types
export const SECURITY_EVENTS = {
  XSS_ATTEMPT: 'xss_attempt',
  SQL_INJECTION_ATTEMPT: 'sql_injection_attempt',
  RATE_LIMIT_EXCEEDED: 'rate_limit_exceeded',
  SUSPICIOUS_REQUEST: 'suspicious_request',
  CSP_VIOLATION: 'csp_violation',
  UNAUTHORIZED_ACCESS: 'unauthorized_access',
} as const;

export type SecurityEventType = typeof SECURITY_EVENTS[keyof typeof SECURITY_EVENTS];

interface SecurityEvent {
  type: SecurityEventType;
  message: string;
  data?: any;
  timestamp: number;
  userAgent?: string;
  ip?: string;
  url?: string;
}

// Security logger
class SecurityLogger {
  private events: SecurityEvent[] = [];
  private readonly maxEvents = 1000;

  log(event: Omit<SecurityEvent, 'timestamp'>): void {
    const securityEvent: SecurityEvent = {
      ...event,
      timestamp: Date.now(),
    };

    this.events.unshift(securityEvent);
    
    // Keep only the most recent events
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(0, this.maxEvents);
    }

    // Log to console in development
    if (import.meta.env.DEV) {
      console.warn(`🔒 Security Event [${event.type}]:`, event.message, event.data);
    }

    // Store in localStorage for analysis
    try {
      localStorage.setItem('sabone-security-events', JSON.stringify(this.events.slice(0, 100)));
    } catch (error) {
      console.warn('Failed to store security events:', error);
    }

    // Send to monitoring service in production
    if (!import.meta.env.DEV) {
      this.sendToMonitoring(securityEvent);
    }
  }

  private sendToMonitoring(event: SecurityEvent): void {
    // In production, send to your monitoring service
    // Example: Sentry, LogRocket, or custom monitoring endpoint
    try {
      // fetch('/api/security-events', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(event)
      // });
    } catch (error) {
      console.error('Failed to send security event to monitoring:', error);
    }
  }

  getEvents(): SecurityEvent[] {
    return [...this.events];
  }

  clearEvents(): void {
    this.events = [];
    localStorage.removeItem('sabone-security-events');
  }
}

const securityLogger = new SecurityLogger();

/**
 * Content Security Policy configuration
 */
const CSP_DIRECTIVES = {
  'default-src': ["'self'"],
  'script-src': [
    "'self'",
    "'unsafe-inline'", // Required for Vite in development
    "'unsafe-eval'", // Required for development
    'https://js.stripe.com',
    'https://www.paypal.com',
    'https://www.paypalobjects.com',
    'https://www.google-analytics.com',
    'https://cdn.auth0.com',
    'https://*.auth0.com',
  ],
  'style-src': [
    "'self'",
    "'unsafe-inline'", // Required for CSS-in-JS and Tailwind
    'https://fonts.googleapis.com',
  ],
  'font-src': [
    "'self'",
    'https://fonts.gstatic.com',
  ],
  'img-src': [
    "'self'",
    'data:',
    'blob:',
    'https://*.stripe.com',
    'https://*.paypal.com',
    'https://www.google-analytics.com',
    'https://*.auth0.com',
  ],
  'connect-src': [
    "'self'",
    'https://api.stripe.com',
    'https://*.paypal.com',
    'https://www.google-analytics.com',
    'https://*.auth0.com',
    'wss://localhost:*', // For development
  ],
  'frame-src': [
    'https://js.stripe.com',
    'https://hooks.stripe.com',
    'https://*.paypal.com',
    'https://*.auth0.com',
  ],
  'object-src': ["'none'"],
  'base-uri': ["'self'"],
  'form-action': ["'self'"],
  'frame-ancestors': ["'none'"],
};

/**
 * Initialize Content Security Policy
 */
export const initializeCSP = (): void => {
  if (typeof document === 'undefined') return;

  // Don't apply strict CSP in development mode
  if (import.meta.env.DEV) {
    console.info('🔒 CSP: Development mode - using relaxed policies');
    return;
  }

  const csp = Object.entries(CSP_DIRECTIVES)
    .map(([directive, sources]) => `${directive} ${sources.join(' ')}`)
    .join('; ');

  // Create meta tag for CSP
  const meta = document.createElement('meta');
  meta.httpEquiv = 'Content-Security-Policy';
  meta.content = csp;
  document.head.appendChild(meta);

  // Listen for CSP violations
  document.addEventListener('securitypolicyviolation', (event) => {
    securityLogger.log({
      type: SECURITY_EVENTS.CSP_VIOLATION,
      message: `CSP violation: ${event.violatedDirective}`,
      data: {
        directive: event.violatedDirective,
        blockedURI: event.blockedURI,
        originalPolicy: event.originalPolicy,
      },
      url: window.location.href,
      userAgent: navigator.userAgent,
    });
  });

  console.info('🔒 CSP: Content Security Policy initialized');
};

/**
 * Validates and sanitizes form data
 */
export const validateFormData = <T extends Record<string, any>>(
  data: T,
  fieldConfig?: Record<keyof T, any>
): { isValid: boolean; sanitizedData: T; errors: string[] } => {
  const errors: string[] = [];
  let isValid = true;

  // Check for XSS patterns
  for (const [key, value] of Object.entries(data)) {
    if (typeof value === 'string') {
      if (containsXssPatterns(value)) {
        errors.push(`Potential XSS detected in ${key}`);
        isValid = false;
        securityLogger.log({
          type: SECURITY_EVENTS.XSS_ATTEMPT,
          message: `XSS attempt detected in form field: ${key}`,
          data: { field: key, value: value.substring(0, 100) },
          url: window.location.href,
        });
      }

      if (containsSqlInjection(value)) {
        errors.push(`Potential SQL injection detected in ${key}`);
        isValid = false;
        securityLogger.log({
          type: SECURITY_EVENTS.SQL_INJECTION_ATTEMPT,
          message: `SQL injection attempt detected in form field: ${key}`,
          data: { field: key, value: value.substring(0, 100) },
          url: window.location.href,
        });
      }
    }
  }

  // Sanitize the data
  const sanitizedData = sanitizeObject(data, fieldConfig);

  return { isValid, sanitizedData, errors };
};

/**
 * Rate limiting for sensitive operations
 */
export const checkRateLimit = (
  operation: string,
  identifier: string = 'global',
  maxAttempts: number = 5,
  windowMs: number = 15 * 60 * 1000
): boolean => {
  const key = `${operation}_${identifier}`;
  const allowed = validateRateLimit(key, maxAttempts, windowMs);

  if (!allowed) {
    securityLogger.log({
      type: SECURITY_EVENTS.RATE_LIMIT_EXCEEDED,
      message: `Rate limit exceeded for operation: ${operation}`,
      data: { operation, identifier, maxAttempts, windowMs },
      url: window.location.href,
    });

    toast.error('Too many attempts. Please try again later.');
  }

  return allowed;
};

/**
 * Secure request interceptor
 */
export const secureRequestInterceptor = (
  url: string,
  options: RequestInit = {}
): { url: string; options: RequestInit; allowed: boolean } => {
  let allowed = true;
  const secureOptions = { ...options };

  // Add security headers
  secureOptions.headers = {
    ...secureOptions.headers,
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
  };

  // Validate request body for suspicious content
  if (secureOptions.body && typeof secureOptions.body === 'string') {
    try {
      const bodyData = JSON.parse(secureOptions.body);
      const validation = validateFormData(bodyData);
      
      if (!validation.isValid) {
        allowed = false;
        securityLogger.log({
          type: SECURITY_EVENTS.SUSPICIOUS_REQUEST,
          message: 'Blocked suspicious request',
          data: { url, errors: validation.errors },
          url: window.location.href,
        });
      } else {
        secureOptions.body = JSON.stringify(validation.sanitizedData);
      }
    } catch {
      // Body is not JSON, skip validation
    }
  }

  // Check for suspicious URLs
  if (url.includes('javascript:') || url.includes('data:')) {
    allowed = false;
    securityLogger.log({
      type: SECURITY_EVENTS.SUSPICIOUS_REQUEST,
      message: 'Blocked request to suspicious URL',
      data: { url },
      url: window.location.href,
    });
  }

  return { url, options: secureOptions, allowed };
};

/**
 * Initialize security middleware
 */
export const initializeSecurity = (config: Partial<SecurityConfig> = {}): void => {
  const securityConfig = { ...DEFAULT_SECURITY_CONFIG, ...config };

  console.info('🔒 Initializing security middleware...');

  // Initialize CSP
  if (securityConfig.enableCSP) {
    initializeCSP();
  }

  // Intercept fetch requests
  if (securityConfig.enableInputValidation || securityConfig.blockSuspiciousRequests) {
    const originalFetch = window.fetch;
    window.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
      const url = typeof input === 'string' ? input : input instanceof URL ? input.href : input.url;
      const { url: secureUrl, options: secureOptions, allowed } = secureRequestInterceptor(url, init);

      if (!allowed && securityConfig.blockSuspiciousRequests) {
        throw new Error('Request blocked by security middleware');
      }

      return originalFetch(secureUrl, secureOptions);
    };
  }

  // Monitor for suspicious activity
  if (securityConfig.logSecurityEvents) {
    // Monitor console for potential XSS attempts
    const originalLog = console.log;
    console.log = (...args) => {
      const message = args.join(' ');
      if (containsXssPatterns(message)) {
        securityLogger.log({
          type: SECURITY_EVENTS.XSS_ATTEMPT,
          message: 'Potential XSS detected in console output',
          data: { message: message.substring(0, 200) },
          url: window.location.href,
        });
      }
      originalLog.apply(console, args);
    };

    // Monitor for unauthorized DOM access attempts
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              
              // Check for suspicious script injections
              if (element.tagName === 'SCRIPT' || element.innerHTML?.includes('<script')) {
                securityLogger.log({
                  type: SECURITY_EVENTS.XSS_ATTEMPT,
                  message: 'Suspicious script element detected',
                  data: { 
                    tagName: element.tagName, 
                    innerHTML: element.innerHTML?.substring(0, 200) 
                  },
                  url: window.location.href,
                });
              }
            }
          });
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });
  }

  // Set secure cookie defaults if in browser
  if (typeof document !== 'undefined') {
    // Override document.cookie to add security flags
    const originalCookieDescriptor = Object.getOwnPropertyDescriptor(Document.prototype, 'cookie') || 
                                   Object.getOwnPropertyDescriptor(HTMLDocument.prototype, 'cookie');

    if (originalCookieDescriptor && originalCookieDescriptor.set) {
      Object.defineProperty(document, 'cookie', {
        get: originalCookieDescriptor.get,
        set: function(cookieString: string) {
          // Add security flags to cookies
          let secureCookie = cookieString;
          if (!secureCookie.includes('Secure') && window.location.protocol === 'https:') {
            secureCookie += '; Secure';
          }
          if (!secureCookie.includes('SameSite')) {
            secureCookie += '; SameSite=Strict';
          }
          if (!secureCookie.includes('HttpOnly') && !secureCookie.includes('httponly')) {
            // Note: HttpOnly can't be set from JavaScript, but we log this for awareness
            console.info('🔒 Consider setting HttpOnly flag on sensitive cookies server-side');
          }
          
          originalCookieDescriptor.set!.call(this, secureCookie);
        },
        enumerable: true,
        configurable: true,
      });
    }
  }

  console.info('🔒 Security middleware initialized successfully');
};

/**
 * Get security events for analysis
 */
export const getSecurityEvents = (): SecurityEvent[] => {
  return securityLogger.getEvents();
};

/**
 * Clear security events
 */
export const clearSecurityEvents = (): void => {
  securityLogger.clearEvents();
};

/**
 * Check if user session is secure
 */
export const validateSecureSession = (): boolean => {
  // Check if running over HTTPS in production
  if (!import.meta.env.DEV && window.location.protocol !== 'https:') {
    securityLogger.log({
      type: SECURITY_EVENTS.UNAUTHORIZED_ACCESS,
      message: 'Insecure connection detected in production',
      url: window.location.href,
    });
    return false;
  }

  // Check for session integrity
  const hasSecureCookies = document.cookie.includes('Secure');
  const hasSameSite = document.cookie.includes('SameSite');
  
  if (!hasSecureCookies || !hasSameSite) {
    console.warn('🔒 Session cookies may not have proper security flags');
  }

  return true;
};

/**
 * Validate file upload security
 */
export const validateFileUpload = (file: File): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'application/pdf'];
  const maxSize = 10 * 1024 * 1024; // 10MB
  const maxNameLength = 255;

  // Check file type
  if (!allowedTypes.includes(file.type)) {
    errors.push('File type not allowed');
  }

  // Check file size
  if (file.size > maxSize) {
    errors.push('File size exceeds limit (10MB)');
  }

  // Check filename
  if (file.name.length > maxNameLength) {
    errors.push('Filename too long');
  }

  // Check for suspicious filename patterns
  const suspiciousPatterns = ['.exe', '.bat', '.cmd', '.scr', '.vbs', '.js', '.jar', '.php', '.asp'];
  if (suspiciousPatterns.some(pattern => file.name.toLowerCase().includes(pattern))) {
    errors.push('Suspicious file extension detected');
    securityLogger.log({
      type: SECURITY_EVENTS.SUSPICIOUS_REQUEST,
      message: 'Suspicious file upload attempt',
      data: { filename: file.name, type: file.type, size: file.size },
      url: window.location.href,
    });
  }

  return { valid: errors.length === 0, errors };
};

export default {
  initializeSecurity,
  validateFormData,
  checkRateLimit,
  secureRequestInterceptor,
  validateSecureSession,
  validateFileUpload,
  getSecurityEvents,
  clearSecurityEvents,
  SECURITY_EVENTS,
}; 