import { Product } from "@/data/products";

export interface InventoryItem {
  productId: string;
  stockQuantity: number;
  lowStockThreshold: number;
  isInStock: boolean;
  lastUpdated: string;
}

export interface InventoryUpdate {
  productId: string;
  quantityChange: number; // Positive for additions, negative for reductions
  reason: InventoryUpdateReason;
}

export type InventoryUpdateReason = 
  | 'purchase' 
  | 'restock' 
  | 'return' 
  | 'adjustment' 
  | 'damaged';

export interface InventoryCheck {
  productId: string;
  requestedQuantity: number;
  available: boolean;
  availableQuantity: number;
}

// Helper function to check if a product is in stock
export const isProductInStock = (
  product: Product, 
  inventory: InventoryItem[]
): boolean => {
  const inventoryItem = inventory.find(item => item.productId === product.id);
  return inventoryItem ? inventoryItem.isInStock : false;
};

// Helper function to get the available stock quantity for a product
export const getStockQuantity = (
  product: Product, 
  inventory: InventoryItem[]
): number => {
  const inventoryItem = inventory.find(item => item.productId === product.id);
  return inventoryItem ? inventoryItem.stockQuantity : 0;
};

// Helper function to check if a product is low in stock
export const isLowStock = (
  product: Product, 
  inventory: InventoryItem[]
): boolean => {
  const inventoryItem = inventory.find(item => item.productId === product.id);
  if (!inventoryItem) return false;
  
  return (
    inventoryItem.isInStock && 
    inventoryItem.stockQuantity <= inventoryItem.lowStockThreshold
  );
};
