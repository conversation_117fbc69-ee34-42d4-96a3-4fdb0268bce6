import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';
import { PayPalButtons, usePayPalScriptReducer } from '@paypal/react-paypal-js';
import { createPayPalOrder, capturePayPalPayment } from '@/services/paymentService';

interface PayPalPaymentProps {
  amount: number;
  orderId: string;
  onSuccess: (transactionId: string) => void;
  onError: (error: Error) => void;
  disabled?: boolean;
}

const PayPalPayment = ({
  amount,
  orderId,
  onSuccess,
  onError,
  disabled = false
}: PayPalPaymentProps) => {
  const [{ isPending, isResolved, isRejected }] = usePayPalScriptReducer();
  const [error, setError] = useState<string | null>(null);

  // Create PayPal order
  const handleCreateOrder = async () => {
    try {
      const result = await createPayPalOrder(amount);
      return result.orderId;
    } catch (error) {
      console.error('Error creating PayPal order:', error);
      setError('Failed to create PayPal order. Please try again.');
      onError(error instanceof Error ? error : new Error('Failed to create PayPal order'));
      throw error;
    }
  };

  // Capture PayPal payment
  const handleApprove = async (data: { orderID: string }) => {
    try {
      const result = await capturePayPalPayment(data.orderID);

      if (result.status === 'COMPLETED' || result.status === 'APPROVED') {
        toast.success('PayPal payment processed successfully');
        onSuccess(result.paymentId);
        return result;
      } else {
        throw new Error(`Payment not completed: ${result.status}`);
      }
    } catch (error) {
      console.error('Error capturing PayPal payment:', error);
      setError('Failed to complete PayPal payment. Please try again.');
      onError(error instanceof Error ? error : new Error('Failed to capture PayPal payment'));
      throw error;
    }
  };

  // Handle PayPal errors
  const handleError = (err: Record<string, unknown>) => {
    console.error('PayPal error:', err);
    setError('An error occurred with PayPal. Please try again.');
    onError(new Error(err.message as string || 'PayPal payment failed'));
  };

  // Handle PayPal cancel
  const handleCancel = () => {
    setError('Payment was cancelled. Please try again.');
    onError(new Error('PayPal payment cancelled'));
  };

  if (isPending) {
    return (
      <div className="flex flex-col items-center justify-center py-6">
        <Loader2 className="h-8 w-8 animate-spin text-sabone-gold" />
        <p className="text-sabone-cream mt-4">Loading PayPal...</p>
      </div>
    );
  }

  if (isRejected) {
    return (
      <div className="p-6 bg-sabone-charcoal/50 rounded-md border border-sabone-gold/20">
        <p className="text-red-500 text-center">
          Failed to load PayPal. Please refresh the page or try a different payment method.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="p-6 bg-sabone-charcoal/50 rounded-md border border-sabone-gold/20 flex flex-col items-center">
        <div className="mb-4">
          <img
            src="/lovable-uploads/paypal-logo.png"
            alt="PayPal"
            className="h-8"
            onError={(e) => {
              // Fallback if image doesn't exist
              e.currentTarget.style.display = 'none';
            }}
          />
        </div>
        <p className="text-sabone-cream text-center mb-4">
          Complete your payment of ${amount.toFixed(2)} securely with PayPal.
        </p>
      </div>

      {error && (
        <div className="text-red-500 text-sm mt-2">
          {error}
        </div>
      )}

      <div className="mt-4">
        <PayPalButtons
          style={{
            color: 'gold',
            shape: 'rect',
            label: 'pay',
            height: 50,
            layout: 'vertical'
          }}
          disabled={disabled}
          forceReRender={[amount, disabled]}
          createOrder={handleCreateOrder}
          onApprove={handleApprove}
          onError={handleError}
          onCancel={handleCancel}
        />
      </div>
    </div>
  );
};

export default PayPalPayment;
