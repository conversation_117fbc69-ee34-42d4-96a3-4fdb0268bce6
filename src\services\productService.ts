/**
 * Product Service
 * 
 * Service layer for product-related API operations
 * with proper typing and business logic.
 */

import { api, ApiResponse } from './api';

// Product types
export interface Product {
  id: string;
  name: string;
  description: string;
  shortDescription?: string;
  price: number;
  originalPrice?: number;
  discountPercentage?: number;
  currency: string;
  category: string;
  subcategory?: string;
  brand?: string;
  sku: string;
  slug: string;
  images: ProductImage[];
  thumbnail?: string;
  gallery?: string[];
  inStock: boolean;
  stockQuantity: number;
  lowStockThreshold?: number;
  weight?: number;
  dimensions?: ProductDimensions;
  tags: string[];
  features: string[];
  specifications?: Record<string, string>;
  variants?: ProductVariant[];
  reviews?: ProductReview[];
  averageRating?: number;
  totalReviews?: number;
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
  isActive: boolean;
  isFeatured: boolean;
  isOnSale: boolean;
  isDigital: boolean;
  downloadUrl?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ProductImage {
  id: string;
  url: string;
  alt: string;
  caption?: string;
  isMain?: boolean;
  sortOrder: number;
}

export interface ProductDimensions {
  length: number;
  width: number;
  height: number;
  unit: 'cm' | 'in';
}

export interface ProductVariant {
  id: string;
  name: string;
  value: string;
  price?: number;
  sku?: string;
  inStock: boolean;
  stockQuantity: number;
}

export interface ProductReview {
  id: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  rating: number;
  title?: string;
  comment: string;
  isVerifiedPurchase: boolean;
  isRecommended?: boolean;
  helpful: number;
  createdAt: string;
}

// Filter and search types
export interface ProductFilters {
  category?: string;
  subcategory?: string;
  brand?: string;
  minPrice?: number;
  maxPrice?: number;
  inStock?: boolean;
  isOnSale?: boolean;
  isFeatured?: boolean;
  tags?: string[];
  rating?: number;
  sortBy?: ProductSortOption;
  search?: string;
}

export type ProductSortOption = 
  | 'newest'
  | 'oldest'
  | 'price-low'
  | 'price-high'
  | 'rating'
  | 'popularity'
  | 'alphabetical';

export interface ProductSearchParams extends ProductFilters {
  page?: number;
  limit?: number;
}

// Request/Response types
export interface CreateProductRequest {
  name: string;
  description: string;
  shortDescription?: string;
  price: number;
  originalPrice?: number;
  currency: string;
  category: string;
  subcategory?: string;
  brand?: string;
  sku: string;
  images: Omit<ProductImage, 'id'>[];
  inStock: boolean;
  stockQuantity: number;
  lowStockThreshold?: number;
  weight?: number;
  dimensions?: ProductDimensions;
  tags: string[];
  features: string[];
  specifications?: Record<string, string>;
  variants?: Omit<ProductVariant, 'id'>[];
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
  isActive: boolean;
  isFeatured: boolean;
  isOnSale: boolean;
  isDigital: boolean;
  downloadUrl?: string;
}

export interface UpdateProductRequest extends Partial<CreateProductRequest> {
  id: string;
}

export interface ProductListResponse {
  products: Product[];
  filters: {
    categories: string[];
    brands: string[];
    priceRange: { min: number; max: number };
    tags: string[];
  };
}

/**
 * Product Service Class
 */
export class ProductService {
  /**
   * Get all products with optional filtering and pagination
   */
  async getProducts(params: ProductSearchParams = {}): Promise<ApiResponse<ProductListResponse>> {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          value.forEach(item => searchParams.append(key, item.toString()));
        } else {
          searchParams.append(key, value.toString());
        }
      }
    });

    const url = `/products${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return api.get<ProductListResponse>(url);
  }

  /**
   * Get a single product by ID
   */
  async getProduct(id: string): Promise<ApiResponse<Product>> {
    return api.get<Product>(`/products/${id}`);
  }

  /**
   * Get a product by slug
   */
  async getProductBySlug(slug: string): Promise<ApiResponse<Product>> {
    return api.get<Product>(`/products/slug/${slug}`);
  }

  /**
   * Create a new product
   */
  async createProduct(productData: CreateProductRequest): Promise<ApiResponse<Product>> {
    return api.post<Product>('/products', productData);
  }

  /**
   * Update an existing product
   */
  async updateProduct(id: string, productData: Partial<UpdateProductRequest>): Promise<ApiResponse<Product>> {
    return api.put<Product>(`/products/${id}`, productData);
  }

  /**
   * Delete a product
   */
  async deleteProduct(id: string): Promise<ApiResponse<{ success: boolean }>> {
    return api.delete<{ success: boolean }>(`/products/${id}`);
  }

  /**
   * Get featured products
   */
  async getFeaturedProducts(limit: number = 8): Promise<ApiResponse<Product[]>> {
    return api.get<Product[]>(`/products/featured?limit=${limit}`);
  }

  /**
   * Get products on sale
   */
  async getSaleProducts(limit: number = 12): Promise<ApiResponse<Product[]>> {
    return api.get<Product[]>(`/products/sale?limit=${limit}`);
  }

  /**
   * Get related products
   */
  async getRelatedProducts(productId: string, limit: number = 6): Promise<ApiResponse<Product[]>> {
    return api.get<Product[]>(`/products/${productId}/related?limit=${limit}`);
  }

  /**
   * Search products
   */
  async searchProducts(query: string, filters: ProductFilters = {}): Promise<ApiResponse<ProductListResponse>> {
    return this.getProducts({ ...filters, search: query });
  }

  /**
   * Get product categories
   */
  async getCategories(): Promise<ApiResponse<string[]>> {
    return api.get<string[]>('/products/categories');
  }

  /**
   * Get product brands
   */
  async getBrands(): Promise<ApiResponse<string[]>> {
    return api.get<string[]>('/products/brands');
  }

  /**
   * Get product reviews
   */
  async getProductReviews(productId: string, page: number = 1, limit: number = 10): Promise<ApiResponse<ProductReview[]>> {
    return api.get<ProductReview[]>(`/products/${productId}/reviews?page=${page}&limit=${limit}`);
  }

  /**
   * Add a product review
   */
  async addProductReview(
    productId: string, 
    review: {
      rating: number;
      title?: string;
      comment: string;
      isRecommended?: boolean;
    }
  ): Promise<ApiResponse<ProductReview>> {
    return api.post<ProductReview>(`/products/${productId}/reviews`, review);
  }

  /**
   * Mark review as helpful
   */
  async markReviewHelpful(productId: string, reviewId: string): Promise<ApiResponse<{ success: boolean }>> {
    return api.post<{ success: boolean }>(`/products/${productId}/reviews/${reviewId}/helpful`, {});
  }

  /**
   * Upload product images
   */
  async uploadProductImages(productId: string, files: File[]): Promise<ApiResponse<ProductImage[]>> {
    const formData = new FormData();
    files.forEach((file, index) => {
      formData.append(`images`, file);
    });

    return api.upload<ProductImage[]>(`/products/${productId}/images`, files[0], {
      fieldName: 'images'
    });
  }

  /**
   * Update product stock
   */
  async updateStock(productId: string, quantity: number): Promise<ApiResponse<{ stockQuantity: number }>> {
    return api.put<{ stockQuantity: number }>(`/products/${productId}/stock`, { quantity });
  }

  /**
   * Toggle product active status
   */
  async toggleProductStatus(productId: string): Promise<ApiResponse<{ isActive: boolean }>> {
    return api.put<{ isActive: boolean }>(`/products/${productId}/toggle-status`, {});
  }

  /**
   * Generate product slug from name
   */
  generateSlug(name: string): string {
    return name
      .toLowerCase()
      .trim()
      .replace(/[^\w\s-]/g, '')
      .replace(/[\s_-]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }

  /**
   * Calculate discount percentage
   */
  calculateDiscount(originalPrice: number, salePrice: number): number {
    if (originalPrice <= 0 || salePrice >= originalPrice) return 0;
    return Math.round(((originalPrice - salePrice) / originalPrice) * 100);
  }

  /**
   * Format price with currency
   */
  formatPrice(price: number, currency: string = 'USD'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
    }).format(price);
  }

  /**
   * Check if product is in stock
   */
  isInStock(product: Product): boolean {
    return product.inStock && product.stockQuantity > 0;
  }

  /**
   * Check if product is low stock
   */
  isLowStock(product: Product): boolean {
    if (!product.lowStockThreshold) return false;
    return product.stockQuantity <= product.lowStockThreshold;
  }

  /**
   * Get product availability status
   */
  getAvailabilityStatus(product: Product): 'in-stock' | 'low-stock' | 'out-of-stock' {
    if (!this.isInStock(product)) return 'out-of-stock';
    if (this.isLowStock(product)) return 'low-stock';
    return 'in-stock';
  }
}

// Export singleton instance
export const productService = new ProductService();
