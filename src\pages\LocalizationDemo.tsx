import React from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { localeNames, isRTL as checkIsRTL } from '@/i18n/config';
import { useRTL } from '@/hooks/rtl-hooks'; // Keep this for now, might be used for dir
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { LanguageSwitcher, SimpleLanguageSwitcher } from '@/components/i18n/LanguageSwitcher';
import CurrentLanguage from '@/components/i18n/CurrentLanguage';
import DirectionIndicator from '@/components/i18n/DirectionIndicator';
import { Globe, Languages, ArrowLeftRight, Check, Info } from 'lucide-react';

/**
 * Localization Demo Page
 * This page showcases the localization features of the application
 */
const LocalizationDemo: React.FC = () => {
  const t = useTranslations();
  const locale = useLocale(); // Get current locale from next-intl
  const isRTL = checkIsRTL(locale); // Use the isRTL from config
  const { dir } = useRTL(); // Keep this for dir attribute if needed, or remove if not used

  return (
    <div className="container mx-auto py-12 px-4">
      <h1 className="text-3xl font-playfair text-sabone-gold mb-2">
        {t('common.languageSwitcher.label')}
      </h1>
      <p className="text-sabone-cream/70 mb-8">
        This page demonstrates the localization features of Sabone.store
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
        <Card className="bg-sabone-dark-olive/20 border-sabone-gold/20">
          <CardHeader>
            <CardTitle className="text-sabone-gold flex items-center gap-2">
              <Globe className="h-5 w-5" />
              {t('common.languageSwitcher.currentLanguage')}
            </CardTitle>
            <CardDescription className="text-sabone-cream/70">
              Current language and direction settings
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex flex-col gap-4">
              <div className="flex justify-between items-center">
                <span className="text-sabone-cream/70">Language:</span>
                <Badge className="bg-sabone-gold/20 text-sabone-gold border-sabone-gold/30">
                  {localeNames[locale as keyof typeof localeNames]}
                </Badge>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sabone-cream/70">Direction:</span>
                <Badge
                  variant="outline"
                  className={`
                    ${isRTL
                      ? 'bg-sabone-dark-olive/70 text-sabone-gold border-sabone-gold/30'
                      : 'bg-sabone-dark-olive/30 text-sabone-cream/70 border-sabone-cream/30'
                    }
                  `}
                >
                  {dir.toUpperCase()}
                </Badge>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sabone-cream/70">RTL Mode:</span>
                <Badge
                  variant={isRTL ? "default" : "outline"}
                  className={isRTL
                    ? "bg-sabone-gold text-sabone-charcoal"
                    : "border-sabone-cream/30 text-sabone-cream/70"
                  }
                >
                  {isRTL ? <Check className="h-3 w-3 mr-1" /> : null}
                  {isRTL ? "Enabled" : "Disabled"}
                </Badge>
              </div>
            </div>

            <Separator className="bg-sabone-gold/20" />

            <div className="flex flex-col gap-4">
              <div className="flex justify-between items-center">
                <span className="text-sabone-cream/70">Language Switcher:</span>
                <LanguageSwitcher />
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sabone-cream/70">Simple Switcher:</span>
                <SimpleLanguageSwitcher />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-sabone-dark-olive/20 border-sabone-gold/20">
          <CardHeader>
            <CardTitle className="text-sabone-gold flex items-center gap-2">
              <Languages className="h-5 w-5" />
              RTL Support Demo
            </CardTitle>
            <CardDescription className="text-sabone-cream/70">
              Examples of RTL-aware components and styling
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex flex-col gap-4">
              <div className="bg-sabone-charcoal/50 p-4 rounded-md">
                <h3 className="text-sabone-gold mb-2">Text Alignment</h3>
                <p className="text-sabone-cream/70 mb-2">
                  This text automatically aligns {isRTL ? "right" : "left"} based on the current language direction.
                </p>
                <p dir="ltr" className="text-sabone-cream/70 mb-2 border-l-2 border-sabone-gold/30 pl-2">
                  This text is explicitly set to LTR direction.
                </p>
                <p dir="rtl" className="text-sabone-cream/70 border-r-2 border-sabone-gold/30 pr-2">
                  This text is explicitly set to RTL direction.
                </p>
              </div>

              <div className="bg-sabone-charcoal/50 p-4 rounded-md">
                <h3 className="text-sabone-gold mb-2">Button Ordering</h3>
                <div className="flex gap-2 mb-2">
                  <Button variant="default" className="bg-sabone-gold text-sabone-charcoal hover:bg-sabone-gold/80">
                    First Button
                  </Button>
                  <Button variant="outline" className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10">
                    Second Button
                  </Button>
                </div>
                <p className="text-sabone-cream/70 text-sm">
                  <Info className="h-3 w-3 inline mr-1" />
                  In RTL mode, the visual order of elements in a flex container is reversed.
                </p>
              </div>

              <div className="bg-sabone-charcoal/50 p-4 rounded-md">
                <h3 className="text-sabone-gold mb-2">Icon Flipping</h3>
                <div className="flex gap-4 items-center">
                  <Badge className="bg-sabone-gold/20 text-sabone-gold border-sabone-gold/30">
                    <ArrowLeftRight className="h-3 w-3 mr-1" />
                    Normal
                  </Badge>
                  <Badge className="bg-sabone-gold/20 text-sabone-gold border-sabone-gold/30">
                    <ArrowLeftRight className="h-3 w-3 mr-1 transform scale-x-[-1]" />
                    Flipped
                  </Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-center">
        <Button
          variant="outline"
          className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
          onClick={() => window.history.back()}
        >
          {t('common.buttons.back')}
        </Button>
      </div>
    </div>
  );
};

export default LocalizationDemo;
