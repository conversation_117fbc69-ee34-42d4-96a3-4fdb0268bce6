import { useEffect, useState } from 'react';
import { useIntl } from '../IntlProvider';

/**
 * Hook to check if the current locale is RTL
 * @returns True if the current locale is RTL, false otherwise
 */
export const useRTL = () => {
  const { isRTL } = useIntl();
  return isRTL;
};

/**
 * Hook to get RTL-aware class names
 * @param ltrClasses Classes to apply in LTR mode
 * @param rtlClasses Classes to apply in RTL mode
 * @returns The appropriate classes based on the current direction
 */
export const useRTLClasses = (ltrClasses: string, rtlClasses: string) => {
  const isRTL = useRTL();
  return isRTL ? rtlClasses : ltrClasses;
};

/**
 * Hook to get RTL-aware styles
 * @param ltrStyles Styles to apply in LTR mode
 * @param rtlStyles Styles to apply in RTL mode
 * @returns The appropriate styles based on the current direction
 */
export const useRTLStyles = <T extends Record<string, any>>(ltrStyles: T, rtlStyles: T) => {
  const isRTL = useRTL();
  return isRTL ? rtlStyles : ltrStyles;
};

/**
 * Hook to get RTL-aware flex direction
 * @returns 'flex-row-reverse' for RTL, 'flex-row' for LTR
 */
export const useRTLFlexDirection = () => {
  const isRTL = useRTL();
  return isRTL ? 'flex-row-reverse' : 'flex-row';
};

/**
 * Hook to get RTL-aware text alignment
 * @returns 'text-right' for RTL, 'text-left' for LTR
 */
export const useRTLTextAlign = () => {
  const isRTL = useRTL();
  return isRTL ? 'text-right' : 'text-left';
};

/**
 * Hook to get RTL-aware margin classes
 * @returns Object with start and end margin classes
 */
export const useRTLMargins = () => {
  const isRTL = useRTL();
  return {
    marginStart: isRTL ? 'mr' : 'ml',
    marginEnd: isRTL ? 'ml' : 'mr',
  };
};

/**
 * Hook to get RTL-aware padding classes
 * @returns Object with start and end padding classes
 */
export const useRTLPaddings = () => {
  const isRTL = useRTL();
  return {
    paddingStart: isRTL ? 'pr' : 'pl',
    paddingEnd: isRTL ? 'pl' : 'pr',
  };
};

/**
 * Hook to get RTL-aware border classes
 * @returns Object with start and end border classes
 */
export const useRTLBorders = () => {
  const isRTL = useRTL();
  return {
    borderStart: isRTL ? 'border-r' : 'border-l',
    borderEnd: isRTL ? 'border-l' : 'border-r',
  };
};

/**
 * Hook to get RTL-aware rounded corner classes
 * @returns Object with start and end rounded corner classes
 */
export const useRTLRounded = () => {
  const isRTL = useRTL();
  return {
    roundedStart: isRTL ? 'rounded-r' : 'rounded-l',
    roundedEnd: isRTL ? 'rounded-l' : 'rounded-r',
    roundedTopStart: isRTL ? 'rounded-tr' : 'rounded-tl',
    roundedTopEnd: isRTL ? 'rounded-tl' : 'rounded-tr',
    roundedBottomStart: isRTL ? 'rounded-br' : 'rounded-bl',
    roundedBottomEnd: isRTL ? 'rounded-bl' : 'rounded-br',
  };
};
