import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { Product } from '@/data/products';
import { useProducts } from '@/contexts/ProductContext';
import { useCart } from '@/contexts/CartContext';
import { useAuth } from '@/contexts/AuthContext';
import { recommendationService } from '@/services/recommendationService';
import {
  RecommendationResult,
  trackProductView,
  trackCartAddition,
  clearOldBehaviorData,
  getSessionId
} from '@/utils/recommendationUtils';
import { logger } from '@/utils/logger';

interface RecommendationContextType {
  // Recommendation methods
  getCustomersAlsoBought: (productId: string) => Promise<Product[]>;
  getRecommendedForYou: () => Promise<Product[]>;
  getRecentlyViewed: () => Promise<Product[]>;
  getFrequentlyBoughtTogether: (cartProductIds: string[]) => Promise<Product[]>;
  getTrendingProducts: () => Promise<Product[]>;
  
  // Tracking methods
  trackProductView: (productId: string, duration?: number) => void;
  trackCartAddition: (productId: string, quantity: number) => void;
  
  // State
  loading: boolean;
  error: string | null;
  
  // Cache management
  clearRecommendationCache: () => void;
  refreshRecommendations: () => Promise<void>;
}

const RecommendationContext = createContext<RecommendationContextType | undefined>(undefined);

export const RecommendationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { products, getProductsByIds } = useProducts();
  const { items: cartItems } = useCart();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize and clean up old data on mount
  useEffect(() => {
    clearOldBehaviorData();
    
    // Set up periodic cleanup
    const cleanupInterval = setInterval(() => {
      clearOldBehaviorData();
    }, 60 * 60 * 1000); // Every hour

    return () => clearInterval(cleanupInterval);
  }, []);

  // Track user authentication changes
  useEffect(() => {
    if (user) {
      logger.userAction('user_authenticated', { userId: user.sub });
    }
  }, [user]);

  /**
   * Get "Customers Also Bought" recommendations
   */
  const getCustomersAlsoBought = useCallback(async (productId: string): Promise<Product[]> => {
    try {
      setLoading(true);
      setError(null);

      const result = await recommendationService.getCustomersAlsoBought(productId, products);
      const recommendedProducts = getProductsByIds(result.recommendations.map(r => r.productId));

      logger.userAction('recommendation_fetched', {
        type: 'customers_also_bought',
        productId,
        count: recommendedProducts.length,
        sessionId: getSessionId()
      });

      return recommendedProducts;
    } catch (err) {
      const errorMessage = 'Failed to get "Customers Also Bought" recommendations';
      setError(errorMessage);
      logger.error(errorMessage, err);
      return [];
    } finally {
      setLoading(false);
    }
  }, [products, getProductsByIds]);

  /**
   * Get "Recommended for You" personalized recommendations
   */
  const getRecommendedForYou = useCallback(async (): Promise<Product[]> => {
    try {
      setLoading(true);
      setError(null);

      const result = await recommendationService.getRecommendedForYou(products);
      const recommendedProducts = getProductsByIds(result.recommendations.map(r => r.productId));

      logger.userAction('recommendation_fetched', {
        type: 'recommended_for_you',
        count: recommendedProducts.length,
        sessionId: getSessionId(),
        userId: user?.sub
      });

      return recommendedProducts;
    } catch (err) {
      const errorMessage = 'Failed to get personalized recommendations';
      setError(errorMessage);
      logger.error(errorMessage, err);
      return [];
    } finally {
      setLoading(false);
    }
  }, [products, getProductsByIds, user]);

  /**
   * Get "Recently Viewed" recommendations
   */
  const getRecentlyViewed = useCallback(async (): Promise<Product[]> => {
    try {
      setLoading(true);
      setError(null);

      const result = await recommendationService.getRecentlyViewed(products);
      const recentProducts = getProductsByIds(result.recommendations.map(r => r.productId));

      logger.userAction('recommendation_fetched', {
        type: 'recently_viewed',
        count: recentProducts.length,
        sessionId: getSessionId()
      });

      return recentProducts;
    } catch (err) {
      const errorMessage = 'Failed to get recently viewed products';
      setError(errorMessage);
      logger.error(errorMessage, err);
      return [];
    } finally {
      setLoading(false);
    }
  }, [products, getProductsByIds]);

  /**
   * Get "Frequently Bought Together" recommendations
   */
  const getFrequentlyBoughtTogether = useCallback(async (cartProductIds: string[]): Promise<Product[]> => {
    try {
      setLoading(true);
      setError(null);

      const result = await recommendationService.getFrequentlyBoughtTogether(cartProductIds, products);
      const recommendedProducts = getProductsByIds(result.recommendations.map(r => r.productId));

      logger.userAction('recommendation_fetched', {
        type: 'frequently_bought_together',
        cartProductIds,
        count: recommendedProducts.length,
        sessionId: getSessionId()
      });

      return recommendedProducts;
    } catch (err) {
      const errorMessage = 'Failed to get "Frequently Bought Together" recommendations';
      setError(errorMessage);
      logger.error(errorMessage, err);
      return [];
    } finally {
      setLoading(false);
    }
  }, [products, getProductsByIds]);

  /**
   * Get trending/popular products
   */
  const getTrendingProducts = useCallback(async (): Promise<Product[]> => {
    try {
      setLoading(true);
      setError(null);

      const result = await recommendationService.getTrendingProducts(products);
      const trendingProducts = getProductsByIds(result.recommendations.map(r => r.productId));

      logger.userAction('recommendation_fetched', {
        type: 'trending',
        count: trendingProducts.length,
        sessionId: getSessionId()
      });

      return trendingProducts;
    } catch (err) {
      const errorMessage = 'Failed to get trending products';
      setError(errorMessage);
      logger.error(errorMessage, err);
      return [];
    } finally {
      setLoading(false);
    }
  }, [products, getProductsByIds]);

  /**
   * Track product view with analytics
   */
  const trackProductViewWithAnalytics = useCallback((productId: string, duration?: number) => {
    try {
      trackProductView(productId, duration);
      
      logger.userAction('product_viewed', {
        productId,
        duration,
        sessionId: getSessionId(),
        userId: user?.sub
      });
    } catch (err) {
      logger.error('Failed to track product view', err);
    }
  }, [user]);

  /**
   * Track cart addition with analytics
   */
  const trackCartAdditionWithAnalytics = useCallback((productId: string, quantity: number) => {
    try {
      trackCartAddition(productId, quantity);
      
      logger.userAction('cart_addition_tracked', {
        productId,
        quantity,
        sessionId: getSessionId(),
        userId: user?.sub
      });
    } catch (err) {
      logger.error('Failed to track cart addition', err);
    }
  }, [user]);

  /**
   * Clear recommendation cache
   */
  const clearRecommendationCache = useCallback(() => {
    try {
      recommendationService.clearCache();
      logger.userAction('recommendation_cache_cleared', {
        sessionId: getSessionId()
      });
    } catch (err) {
      logger.error('Failed to clear recommendation cache', err);
    }
  }, []);

  /**
   * Refresh all recommendations
   */
  const refreshRecommendations = useCallback(async () => {
    try {
      setLoading(true);
      clearRecommendationCache();
      
      // Pre-fetch popular recommendations
      await getTrendingProducts();
      
      logger.userAction('recommendations_refreshed', {
        sessionId: getSessionId()
      });
    } catch (err) {
      logger.error('Failed to refresh recommendations', err);
    } finally {
      setLoading(false);
    }
  }, [getTrendingProducts, clearRecommendationCache]);

  const value: RecommendationContextType = {
    getCustomersAlsoBought,
    getRecommendedForYou,
    getRecentlyViewed,
    getFrequentlyBoughtTogether,
    getTrendingProducts,
    trackProductView: trackProductViewWithAnalytics,
    trackCartAddition: trackCartAdditionWithAnalytics,
    loading,
    error,
    clearRecommendationCache,
    refreshRecommendations
  };

  return (
    <RecommendationContext.Provider value={value}>
      {children}
    </RecommendationContext.Provider>
  );
};

export const useRecommendations = () => {
  const context = useContext(RecommendationContext);
  if (context === undefined) {
    throw new Error('useRecommendations must be used within a RecommendationProvider');
  }
  return context;
};

export default RecommendationContext;
