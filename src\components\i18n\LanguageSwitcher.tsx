import React from 'react';
import { locales, localeNames, setUserLocale, isRTL as checkIsRTL } from '@/i18n/config';
import { useTranslations, useLocale } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Globe } from 'lucide-react';
import { toast } from 'sonner';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useRTL } from '@/hooks/rtl-hooks';

/**
 * Language switcher component
 * Allows users to switch between available languages
 */
export const LanguageSwitcher: React.FC = () => {
  const locale = useLocale(); // Get current locale from next-intl
  const t = useTranslations('common');
  const isRTL = checkIsRTL(locale); // Use the isRTL from config

  const handleLanguageChange = (newLocale: string) => {
    if (newLocale === locale) return;

    const newLocaleName = localeNames[newLocale as keyof typeof localeNames];

    try {
      setUserLocale(newLocale); // Set locale and handle DOM updates via config function

      toast.success(t('languageSwitcher.languageChanged', { language: newLocaleName }), {
        position: isRTL ? 'top-left' : 'top-right',
        duration: 3000,
      });

      // Reload the page to apply the new language
      // This is often necessary for full next-intl re-initialization
      setTimeout(() => {
        window.location.reload();
      }, 500);
    } catch (error) {
      console.error('Error changing language:', error);
      toast.error('Failed to change language. Please try again.');
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="text-sabone-cream hover:text-sabone-gold hover:bg-sabone-dark-olive/30 transition-colors"
          aria-label={t('languageSwitcher.label')}
        >
          <Globe className="h-5 w-5" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="bg-sabone-dark-olive border border-sabone-gold/30">
        {locales.map((loc) => (
          <DropdownMenuItem
            key={loc}
            className={`text-sabone-cream hover:text-sabone-gold hover:bg-sabone-charcoal/50 cursor-pointer ${
              locale === loc ? 'text-sabone-gold font-medium' : ''
            }`}
            onClick={() => handleLanguageChange(loc)}
          >
            {localeNames[loc as keyof typeof localeNames]}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

/**
 * Simple language switcher component
 * Alternative version with a simpler UI
 */
export const SimpleLanguageSwitcher: React.FC = () => {
  const locale = useLocale(); // Get current locale from next-intl
  const t = useTranslations('common');
  const isRTL = checkIsRTL(locale); // Use the isRTL from config

  const handleLanguageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newLocale = e.target.value;
    const newLocaleName = localeNames[newLocale as keyof typeof localeNames];

    try {
      setUserLocale(newLocale); // Set locale and handle DOM updates via config function

      toast.success(t('languageSwitcher.languageChanged', { language: newLocaleName }), {
        position: isRTL ? 'top-left' : 'top-right',
        duration: 3000,
      });

      // Reload the page to apply the new language
      setTimeout(() => {
        window.location.reload();
      }, 500);
    } catch (error) {
      console.error('Error changing language:', error);
      toast.error('Failed to change language. Please try again.');
    }
  };

  return (
    <div className="language-switcher">
      <select
        onChange={handleLanguageChange}
        value={locale}
        className="bg-sabone-charcoal text-sabone-cream border border-sabone-gold/30 rounded-md p-1"
        aria-label={t('languageSwitcher.label')}
      >
        {locales.map((loc) => (
          <option key={loc} value={loc}>
            {localeNames[loc as keyof typeof localeNames]}
          </option>
        ))}
      </select>
    </div>
  );
};

export default LanguageSwitcher;
