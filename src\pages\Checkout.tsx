import { useEffect, useState, Suspense } from "react";
import { <PERSON><PERSON><PERSON>Provider } from 'react-helmet-async';
import { useCart } from "@/contexts/CartContext";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import CartSummary from "@/components/checkout/CartSummary";
import OrderSummary from "@/components/checkout/OrderSummary";
import CustomerForm from "@/components/checkout/CustomerForm";
import { Separator } from "@/components/ui/separator";
import { useIsMobile } from "@/hooks/use-mobile";
import { ShoppingCart, ChevronDown, ChevronUp } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { LazyFrequentlyBoughtTogether } from "@/components/LazyComponents";
import SEO from "@/components/seo/SEO";

const Checkout = () => {
  const { items, itemCount } = useCart();
  const isMobile = useIsMobile();
  const [isCartExpanded, setIsCartExpanded] = useState(false);

  useEffect(() => {
    // Safely add class to body
    try {
      if (document.body && !document.body.classList.contains("bg-sabone-charcoal")) {
        document.body.classList.add("bg-sabone-charcoal");
      }
    } catch (error) {
      console.error("Error adding class to body:", error);
    }

    return () => {
      // Safely remove class from body
      try {
        if (document.body && document.body.classList.contains("bg-sabone-charcoal")) {
          document.body.classList.remove("bg-sabone-charcoal");
        }
      } catch (error) {
        console.error("Error removing class from body:", error);
      }
    };
  }, []);

  const toggleCart = () => {
    setIsCartExpanded(!isCartExpanded);
  };

  return (
    <HelmetProvider>
      <SEO
        title="Checkout"
        description="Complete your purchase of Sabone's handcrafted natural soaps and shampoos."
        canonical="checkout"
        noIndex={true}
      />

      <div className="min-h-screen bg-sabone-charcoal flex flex-col">
        <Navbar />

        <main className="flex-grow py-8 sm:py-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <h1 className="text-2xl sm:text-4xl font-playfair font-bold mb-6 sm:mb-8 text-sabone-gold text-center">
              Checkout
            </h1>

            {itemCount === 0 ? (
              <div className="text-center py-16">
                <h2 className="text-xl font-playfair text-sabone-cream mb-4">Your cart is empty</h2>
                <p className="text-sabone-cream/80 mb-8">Add some products to your cart to proceed with checkout.</p>
                <a
                  href="/"
                  className="inline-block bg-sabone-gold hover:bg-sabone-gold/80 text-sabone-charcoal font-medium px-6 py-3 rounded-md transition-colors"
                >
                  Continue Shopping
                </a>
              </div>
            ) : (
              <>
                {/* Mobile Cart Toggle */}
                {isMobile && (
                  <button
                    onClick={toggleCart}
                    className="flex w-full justify-between items-center bg-sabone-dark-olive/80 p-4 rounded-md mb-4 gold-border"
                  >
                    <div className="flex items-center">
                      <ShoppingCart className="h-5 w-5 text-sabone-gold mr-2" />
                      <span className="text-sabone-cream">Cart Summary ({itemCount} {itemCount === 1 ? 'item' : 'items'})</span>
                    </div>
                    <div className="text-sabone-gold">
                      {isCartExpanded ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
                    </div>
                  </button>
                )}

                <div className="grid lg:grid-cols-3 gap-6 sm:gap-8">
                  <div className="lg:col-span-2 space-y-6 sm:space-y-8">
                    {/* Cart Summary Section - Always visible on desktop, conditionally on mobile */}
                    <section
                      aria-labelledby="cart-heading"
                      className={`bg-sabone-dark-olive/40 p-4 sm:p-6 rounded-lg gold-border ${isMobile && !isCartExpanded ? 'hidden' : 'block'}`}
                    >
                      {!isMobile && (
                        <h2 id="cart-heading" className="text-xl font-playfair font-semibold text-sabone-gold mb-4">
                          Cart Summary
                        </h2>
                      )}
                      <CartSummary />
                    </section>

                    {/* Customer Information Section */}
                    <section aria-labelledby="customer-heading" className="bg-sabone-dark-olive/40 p-4 sm:p-6 rounded-lg gold-border">
                      <h2 id="customer-heading" className="text-xl font-playfair font-semibold text-sabone-gold mb-4">
                        Customer Information
                      </h2>
                      <Separator className="mb-6 bg-sabone-gold/20" />
                      <CustomerForm />
                    </section>
                  </div>

                  {/* Order Summary Section */}
                  <div className="lg:col-span-1">
                    <div className="bg-sabone-dark-olive/40 p-4 sm:p-6 rounded-lg gold-border lg:sticky lg:top-24">
                      {!isMobile && (
                        <>
                          <h2 className="text-xl font-playfair font-semibold text-sabone-gold mb-4">
                            Order Summary
                          </h2>
                          <Separator className="mb-6 bg-sabone-gold/20" />
                        </>
                      )}
                      <OrderSummary />
                    </div>
                  </div>
                </div>

                {/* Frequently Bought Together Section */}
                <Suspense fallback={
                  <div className="mt-8">
                    <Skeleton className="h-[300px] w-full rounded-md bg-sabone-dark-olive/30" />
                  </div>
                }>
                  <LazyFrequentlyBoughtTogether
                    title="Complete Your Order"
                    subtitle="Customers who bought these items also purchased"
                    className="mt-8"
                  />
                </Suspense>
              </>
            )}
          </div>
        </main>

        <Footer />
      </div>
    </HelmetProvider>
  );
};

export default Checkout;
