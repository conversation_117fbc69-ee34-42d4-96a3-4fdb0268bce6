import { useState, useEffect } from "react";
import { useReviews } from "@/contexts/ReviewContext";
import { Review, ReviewFilterOptions, ReviewSortOption } from "@/types/review";
import ReviewItem from "./ReviewItem";
import { StarRating } from "@/components/ui/star-rating";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { ChevronLeft, ChevronRight, Image, Check } from "lucide-react";

interface ReviewListProps {
  productId: string;
}

const ITEMS_PER_PAGE = 5;

const ReviewList = ({ productId }: ReviewListProps) => {
  const { getFilteredProductReviews, isLoadingReviews } = useReviews();
  const [reviews, setReviews] = useState<Review[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filterOptions, setFilterOptions] = useState<ReviewFilterOptions>({
    sortBy: "newest",
    verifiedOnly: false,
    withImages: false,
  });

  useEffect(() => {
    const fetchReviews = async () => {
      setIsLoading(true);
      try {
        const data = await getFilteredProductReviews(productId, filterOptions);
        setReviews(data);
        setTotalPages(Math.max(1, Math.ceil(data.length / ITEMS_PER_PAGE)));
        setCurrentPage(1); // Reset to first page when filters change
      } catch (error) {
        console.error("Error fetching reviews:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchReviews();
  }, [productId, filterOptions, getFilteredProductReviews]);

  const handleSortChange = (value: string) => {
    setFilterOptions({
      ...filterOptions,
      sortBy: value as ReviewSortOption,
    });
  };

  const handleVerifiedOnlyChange = (checked: boolean) => {
    setFilterOptions({
      ...filterOptions,
      verifiedOnly: checked,
    });
  };

  const handleWithImagesChange = (checked: boolean) => {
    setFilterOptions({
      ...filterOptions,
      withImages: checked,
    });
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // Scroll to top of review list
    document.getElementById("review-list-top")?.scrollIntoView({
      behavior: "smooth",
    });
  };

  // Get current page reviews
  const currentReviews = reviews.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );

  if (isLoading) {
    return (
      <div className="animate-pulse space-y-6">
        {[...Array(3)].map((_, index) => (
          <div key={index} className="bg-sabone-dark-olive/40 p-6 rounded-lg">
            <div className="flex items-center justify-between mb-4">
              <div className="h-5 w-32 bg-sabone-gold/20 rounded"></div>
              <div className="h-4 w-24 bg-sabone-gold/20 rounded"></div>
            </div>
            <div className="h-4 w-3/4 bg-sabone-gold/20 rounded mb-2"></div>
            <div className="h-4 w-full bg-sabone-gold/20 rounded mb-2"></div>
            <div className="h-4 w-2/3 bg-sabone-gold/20 rounded"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div id="review-list-top">
      <div className="bg-sabone-dark-olive/40 p-6 rounded-lg gold-border mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
          <h3 className="text-xl font-playfair font-semibold text-sabone-gold">
            {reviews.length} Review{reviews.length !== 1 ? "s" : ""}
          </h3>

          <div className="flex flex-wrap gap-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="verified-only"
                checked={filterOptions.verifiedOnly}
                onCheckedChange={handleVerifiedOnlyChange}
                className="border-sabone-gold/50 data-[state=checked]:bg-sabone-gold data-[state=checked]:text-sabone-charcoal"
              />
              <Label
                htmlFor="verified-only"
                className="text-sabone-cream cursor-pointer"
              >
                Verified only
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="with-images"
                checked={filterOptions.withImages}
                onCheckedChange={handleWithImagesChange}
                className="border-sabone-gold/50 data-[state=checked]:bg-sabone-gold data-[state=checked]:text-sabone-charcoal"
              />
              <Label
                htmlFor="with-images"
                className="text-sabone-cream cursor-pointer"
              >
                With photos
              </Label>
            </div>

            <Select
              value={filterOptions.sortBy}
              onValueChange={handleSortChange}
            >
              <SelectTrigger className="w-[180px] bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent className="bg-sabone-charcoal border-sabone-gold/30 text-sabone-cream">
                <SelectItem value="newest">Newest first</SelectItem>
                <SelectItem value="oldest">Oldest first</SelectItem>
                <SelectItem value="highest-rating">Highest rating</SelectItem>
                <SelectItem value="lowest-rating">Lowest rating</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {reviews.length === 0 ? (
        <div className="bg-sabone-dark-olive/40 p-6 rounded-lg gold-border text-center">
          <p className="text-sabone-cream">No reviews found</p>
        </div>
      ) : (
        <>
          <div className="space-y-6">
            {currentReviews.map((review) => (
              <ReviewItem key={review.id} review={review} />
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center mt-8">
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                {Array.from({ length: totalPages }).map((_, index) => (
                  <Button
                    key={index}
                    variant={currentPage === index + 1 ? "default" : "outline"}
                    size="sm"
                    onClick={() => handlePageChange(index + 1)}
                    className={
                      currentPage === index + 1
                        ? "bg-sabone-gold text-sabone-charcoal hover:bg-sabone-gold/80"
                        : "border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
                    }
                  >
                    {index + 1}
                  </Button>
                ))}
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="border-sabone-gold/30 text-sabone-gold hover:bg-sabone-gold/10"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default ReviewList;
